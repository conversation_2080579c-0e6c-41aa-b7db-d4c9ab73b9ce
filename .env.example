# Telegram Bot Configuration
BOT_TOKEN=your_bot_token_here
BOT_USERNAME=your_bot_username_here

# MongoDB Configuration
MONGODB_URL=mongodb://localhost:27017/
DATABASE_NAME=buttonbot

# Optional: Logging Configuration
# LOG_LEVEL options: DEBUG, INFO, WARNING, ERROR, CRITICAL
# Third-party library logs (httpx, telegram) are automatically suppressed at INFO level
LOG_LEVEL=INFO
LOG_FILE=buttonbot.log

# Optional: Bot Configuration
MAX_BUTTONS_PER_ROW=5
MAX_CHANNELS_PER_USER=10
