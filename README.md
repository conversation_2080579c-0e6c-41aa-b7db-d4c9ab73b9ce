# ButtonBot 🤖

A comprehensive Telegram bot that automatically adds customizable buttons to new posts in Telegram channels. Built with Python using modern libraries and best practices.

## Features ✨

### 🔧 Core Functionality
- **Automatic Button Addition**: Adds configured buttons to all new posts in monitored channels
- **Multi-Channel Support**: Each user can manage multiple channels with independent configurations
- **Custom Button Layouts**: Organize buttons in rows (1-5 buttons per row)
- **Real-time Status Control**: Enable, pause, or disable button addition per channel
- **Smart Permission Handling**: Automatically detects and handles permission changes

### 🎨 User Experience
- **Emoji-Rich Interface**: Intuitive navigation with visual indicators
- **Guided Setup**: Step-by-step channel addition and configuration
- **Button Preview**: See how buttons will appear before saving
- **Status Indicators**: Visual status display (✅ Active / ⏸️ Paused / ⚠️ Disabled)
- **Error Recovery**: Clear error messages with recovery suggestions

### 🔗 Button Support
- **External Websites**: Support for any HTTPS URL
- **Telegram Links**: Channels, bots, and invite links
- **Custom Text**: Personalized button text (up to 64 characters)
- **Flexible URLs**: Automatic protocol detection and validation

## Installation 🚀

### Prerequisites
- Python 3.8 or higher
- MongoDB database
- Telegram Bot Token (from @BotFather)

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd autobuttonbot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   BOT_TOKEN=your_bot_token_here
   BOT_USERNAME=your_bot_username_here
   MONGODB_URL=mongodb://localhost:27017/
   DATABASE_NAME=buttonbot
   ```

4. **Start MongoDB**
   Make sure MongoDB is running on your system.

5. **Run the bot**
   ```bash
   python main.py
   ```

## Configuration ⚙️

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `BOT_TOKEN` | Telegram bot token from @BotFather | - | ✅ |
| `BOT_USERNAME` | Bot username (without @) | - | ✅ |
| `MONGODB_URL` | MongoDB connection URL | `mongodb://localhost:27017/` | ❌ |
| `DATABASE_NAME` | Database name | `buttonbot` | ❌ |
| `MAX_BUTTONS_PER_ROW` | Maximum buttons per row | `5` | ❌ |
| `MAX_CHANNELS_PER_USER` | Maximum channels per user | `10` | ❌ |
| `LOG_LEVEL` | Logging level | `INFO` | ❌ |
| `LOG_FILE` | Log file path | `buttonbot.log` | ❌ |

### Bot Setup

1. **Create a bot** with @BotFather on Telegram
2. **Get the bot token** and add it to your `.env` file
3. **Add the bot** as an administrator to your channels
4. **Grant permissions**: Edit messages, Post messages

## Usage 📱

### Getting Started

1. **Start the bot**: Send `/start` to your bot
2. **Add a channel**: Use "Add Channel" button
3. **Configure buttons**: Set up your custom buttons
4. **Enable the channel**: Activate automatic button addition
5. **Test**: Post a message in your channel to see the buttons

### Commands

- `/start` - Welcome message and main menu
- `/mychannels` - View and manage your channels
- `/settings` - Quick access to channel settings
- `/help` - Comprehensive help documentation

### Channel Management

#### Adding a Channel
1. Make sure you're an admin of the channel
2. Add the bot as administrator with edit/post permissions
3. Use "Add Channel" and provide:
   - Channel username (@mychannel)
   - Channel link (t.me/mychannel)
   - Channel ID (-1001234567890)

#### Button Configuration
1. Select your channel from "My Channels"
2. Choose "Manage Buttons"
3. Add buttons with custom text and URLs
4. Configure layout (buttons per row)
5. Preview your buttons
6. Enable the channel

#### Status Control
- **Active** ✅: Buttons added to all new posts
- **Paused** ⏸️: Temporarily disabled, settings preserved
- **Disabled** ⚠️: Completely turned off

## Architecture 🏗️

### Project Structure
```
autobuttonbot/
├── main.py                 # Application entry point
├── config.py              # Configuration and constants
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── start.jpg             # Welcome image
├── README.md             # Documentation
├── database/             # Database operations
│   ├── __init__.py
│   ├── models.py         # Data models
│   └── operations.py     # CRUD operations
├── handlers/             # Bot command handlers
│   ├── __init__.py
│   ├── start_handler.py  # Start command and main menu
│   ├── channel_handler.py # Channel management
│   ├── settings_handler.py # Button configuration
│   └── message_handler.py # Message monitoring
└── utils/                # Utility functions
    ├── __init__.py
    ├── validators.py     # Input validation
    ├── keyboards.py      # Inline keyboards
    └── helpers.py        # Helper functions
```

### Database Schema

#### Users Collection
```json
{
  "user_id": 123456789,
  "username": "user123",
  "first_name": "John",
  "last_name": "Doe",
  "language_code": "en",
  "is_premium": false,
  "created_at": "2024-01-01T00:00:00Z",
  "last_active": "2024-01-01T00:00:00Z"
}
```

#### Channels Collection
```json
{
  "channel_id": "-1001234567890",
  "channel_name": "My Channel",
  "channel_username": "mychannel",
  "user_id": 123456789,
  "status": "active",
  "buttons": [
    {
      "text": "Visit Website",
      "url": "https://example.com",
      "row": 0,
      "position": 0
    }
  ],
  "max_buttons_per_row": 3,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

## Development 🛠️

### Code Style
- Follow PEP 8 guidelines
- Use type hints where appropriate
- Include docstrings for all functions
- Implement proper error handling

### Testing
```bash
# Run tests (when implemented)
python -m pytest tests/

# Check code style
flake8 .

# Type checking
mypy .
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Troubleshooting 🔧

### Common Issues

**Bot doesn't add buttons to posts**
- Check if bot is admin in the channel
- Verify bot has edit/post message permissions
- Ensure channel status is "Active"
- Check bot logs for error messages

**"Permission Error" when adding channel**
- Add bot as administrator to the channel
- Grant "Edit messages" and "Post messages" permissions
- Try adding the channel again

**Database connection errors**
- Ensure MongoDB is running
- Check MONGODB_URL in .env file
- Verify database permissions

**Bot doesn't respond to commands**
- Check if bot token is correct
- Verify bot is running without errors
- Check network connectivity

### Logs
Check `buttonbot.log` for detailed error information and debugging.

## License 📄

This project is licensed under the MIT License - see the LICENSE file for details.

## Support 💬

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the troubleshooting section

## Changelog 📝

### Version 1.0.0
- Initial release
- Multi-channel support
- Custom button configuration
- Automatic button addition
- Status management
- Comprehensive error handling
