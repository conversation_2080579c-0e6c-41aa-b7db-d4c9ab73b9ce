"""
Database operations for ButtonBot
Handles all MongoDB CRUD operations
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict
from pymongo import MongoClient
from pymongo.errors import PyMongoError

from config import Config
from database.models import User, Channel, Button, ChannelStatus

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database manager for MongoDB operations"""
    
    def __init__(self):
        """Initialize database connection"""
        try:
            self.client = MongoClient(Config.MONGODB_URL)
            self.db = self.client[Config.DATABASE_NAME]
            self.users = self.db.users
            self.channels = self.db.channels
            
            # Create indexes for better performance
            self._create_indexes()
            logger.info("Database connection established successfully")
            
        except PyMongoError as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def _create_indexes(self):
        """Create database indexes"""
        try:
            self.users.create_index("user_id", unique=True)
            self.channels.create_index([("user_id", 1), ("channel_id", 1)], unique=True)
            self.channels.create_index("channel_id")
            logger.info("Database indexes created successfully")
        except PyMongoError as e:
            logger.error(f"Failed to create indexes: {e}")
    
    # User operations
    async def create_user(self, user: User) -> bool:
        """Create or update user in database"""
        try:
            result = self.users.replace_one(
                {"user_id": user.user_id},
                user.to_dict(),
                upsert=True
            )
            logger.info(f"User {user.user_id} created/updated successfully")
            return True
        except PyMongoError as e:
            logger.error(f"Failed to create user {user.user_id}: {e}")
            return False
    
    async def get_user(self, user_id: int) -> Optional[User]:
        """Get user by ID"""
        try:
            user_data = self.users.find_one({"user_id": user_id})
            if user_data:
                return User.from_dict(user_data)
            return None
        except PyMongoError as e:
            logger.error(f"Failed to get user {user_id}: {e}")
            return None
    
    async def update_user_activity(self, user_id: int) -> bool:
        """Update user's last activity timestamp"""
        try:
            result = self.users.update_one(
                {"user_id": user_id},
                {"$set": {"last_active": datetime.utcnow()}}
            )
            return result.modified_count > 0
        except PyMongoError as e:
            logger.error(f"Failed to update user activity {user_id}: {e}")
            return False
    
    # Channel operations
    async def create_channel(self, channel: Channel) -> bool:
        """Create new channel"""
        try:
            result = self.channels.insert_one(channel.to_dict())
            logger.info(f"Channel {channel.channel_id} created successfully")
            return True
        except PyMongoError as e:
            logger.error(f"Failed to create channel {channel.channel_id}: {e}")
            return False
    
    async def get_user_channels(self, user_id: int) -> List[Channel]:
        """Get all channels for a user"""
        try:
            channels_data = self.channels.find({"user_id": user_id})
            return [Channel.from_dict(data) for data in channels_data]
        except PyMongoError as e:
            logger.error(f"Failed to get channels for user {user_id}: {e}")
            return []
    
    async def get_channel(self, user_id: int, channel_id: str) -> Optional[Channel]:
        """Get specific channel for user"""
        try:
            channel_data = self.channels.find_one({
                "user_id": user_id,
                "channel_id": channel_id
            })
            if channel_data:
                return Channel.from_dict(channel_data)
            return None
        except PyMongoError as e:
            logger.error(f"Failed to get channel {channel_id} for user {user_id}: {e}")
            return None
    
    async def update_channel(self, channel: Channel) -> bool:
        """Update channel information"""
        try:
            channel.updated_at = datetime.utcnow()
            result = self.channels.replace_one(
                {"user_id": channel.user_id, "channel_id": channel.channel_id},
                channel.to_dict()
            )
            return result.modified_count > 0
        except PyMongoError as e:
            logger.error(f"Failed to update channel {channel.channel_id}: {e}")
            return False
    
    async def delete_channel(self, user_id: int, channel_id: str) -> bool:
        """Delete channel"""
        try:
            result = self.channels.delete_one({
                "user_id": user_id,
                "channel_id": channel_id
            })
            return result.deleted_count > 0
        except PyMongoError as e:
            logger.error(f"Failed to delete channel {channel_id}: {e}")
            return False
    
    async def get_active_channels(self) -> List[Channel]:
        """Get all active channels for monitoring"""
        try:
            channels_data = self.channels.find({"status": ChannelStatus.ACTIVE.value})
            return [Channel.from_dict(data) for data in channels_data]
        except PyMongoError as e:
            logger.error(f"Failed to get active channels: {e}")
            return []
    
    def close(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            logger.info("Database connection closed")

# Global database instance
db_manager = DatabaseManager()
