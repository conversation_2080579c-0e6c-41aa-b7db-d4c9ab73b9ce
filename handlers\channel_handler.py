"""
Channel management handler for ButtonBot
Handles channel addition, listing, and management
"""

import logging
from telegram import Update
from telegram.ext import ContextTypes, MessageHandler, filters

from config import Emojis, Config
from database.models import Channel, ChannelStatus
from database.operations import db_manager
from utils.helpers import (
    user_required, safe_edit_message, safe_send_message, safe_send_photo_message,
    check_bot_permissions, get_channel_info, format_channel_info
)
from utils.keyboards import (
    channels_list_keyboard, channel_management_keyboard,
    confirmation_keyboard, main_menu_keyboard, add_channel_keyboard, get_admin_url
)
# Validators no longer needed for streamlined approach

logger = logging.getLogger(__name__)

# No conversation states needed - using streamlined approach

@user_required
async def add_channel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle add channel message - new streamlined approach"""
    user_id = update.effective_user.id

    # Check if user has reached channel limit
    user_channels = await db_manager.get_user_channels(user_id)
    if len(user_channels) >= Config.MAX_CHANNELS_PER_USER:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} *Channel Limit Reached*\n\n"
            f"You can add up to {Config.MAX_CHANNELS_PER_USER} channels. "
            f"Please remove some channels before adding new ones.",
            reply_markup=main_menu_keyboard()
        )
        return

    admin_url = get_admin_url(context.bot.username)
    add_channel_text = f"""
{Emojis.ADD} *Add New Channel*

{Emojis.ROBOT} **Easy 2-Step Process:**

{Emojis.INFO} **Step 1:** Click "Open Admin Link" below
• This will open the admin invitation link
• Copy this link: {admin_url}
• Choose your channel from the list
• The bot will be automatically added as admin with all required permissions

{Emojis.SUCCESS} **Step 2:** That's it!
• You'll receive an instant confirmation message
• Your channel will be added to your list
• You can then configure buttons and enable the channel

{Emojis.SETTINGS} **What happens next:**
• Channel starts in "Disabled" status
• Configure your custom buttons
• Set button layout preferences
• Enable automatic button addition
• All new posts will include your buttons!

{Emojis.WARNING} **Note:** You must be an admin of the channel to add the bot.

Ready to add your channel?
"""

    await safe_send_photo_message(update, context, add_channel_text, reply_markup=add_channel_keyboard(context.bot.username))

# Old conversation-based channel input processing removed
# Now using streamlined auto-detection approach

@user_required
async def my_channels_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle my channels message"""
    user_id = update.effective_user.id
    channels = await db_manager.get_user_channels(user_id)

    if not channels:
        no_channels_text = f"""
{Emojis.INFO} *No Channels Added*

You haven't added any channels yet.

{Emojis.CHANNEL} *To get started:*
1. Add @{context.bot.username} as admin to your channel
2. Use "Add Channel" button below
3. Follow the setup wizard
4. Configure your buttons
5. Enable automatic button addition

Ready to add your first channel?
"""
        await safe_send_photo_message(
            update, context, no_channels_text,
            reply_markup=main_menu_keyboard()
        )
        return
    
    channels_text = f"""
{Emojis.LIST} *My Channels ({len(channels)})*

Select a channel to manage its settings:

{Emojis.ACTIVE} Active - Buttons added to new posts
{Emojis.PAUSED} Paused - Temporarily disabled
{Emojis.DISABLED} Disabled - Not adding buttons
"""

    await safe_send_photo_message(
        update, context, channels_text,
        reply_markup=channels_list_keyboard(channels)
    )

@user_required
async def open_admin_link_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle open admin link message"""
    admin_url = get_admin_url(context.bot.username)

    link_text = f"""
{Emojis.ROBOT} *Admin Invitation Link*

Copy this link and open it in Telegram to add the bot as admin to your channel:

`{admin_url}`

{Emojis.INFO} *Instructions:*
1. Copy the link above
2. Open it in Telegram
3. Select your channel
4. Confirm bot permissions
5. Wait for confirmation message

The bot will automatically detect when it's added and send you a confirmation.
"""

    await safe_send_photo_message(
        update, context, link_text,
        reply_markup=add_channel_keyboard(context.bot.username)
    )

@user_required
async def channel_selection_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel selection from list"""
    user_id = update.effective_user.id
    message_text = update.message.text

    # Find the channel by matching the button text
    channels = await db_manager.get_user_channels(user_id)
    selected_channel = None

    for channel in channels:
        status_emoji = {
            ChannelStatus.ACTIVE: Emojis.ACTIVE,
            ChannelStatus.PAUSED: Emojis.PAUSED,
            ChannelStatus.DISABLED: Emojis.DISABLED
        }.get(channel.status, Emojis.DISABLED)

        channel_name = channel.channel_name[:15] + "..." if len(channel.channel_name) > 15 else channel.channel_name
        expected_text = f"{status_emoji} {channel_name}"

        if message_text == expected_text:
            selected_channel = channel
            break

    if not selected_channel:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Store selected channel in context for other handlers
    context.user_data['selected_channel_id'] = selected_channel.channel_id

    channel_text = f"""
{Emojis.CHANNEL} *Channel Details*

{format_channel_info(selected_channel)}

{Emojis.SETTINGS} *Management Options:*
Use the buttons below to manage this channel.
"""

    await safe_send_photo_message(
        update, context, channel_text,
        reply_markup=channel_management_keyboard(selected_channel)
    )

# Status change handlers
@user_required
async def activate_channel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel activation"""
    user_id = update.effective_user.id
    channel_id = context.user_data.get('selected_channel_id')

    if not channel_id:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} No channel selected. Please select a channel first.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Check if channel has buttons configured
    if not channel.buttons:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.WARNING} *No Buttons Configured*\n\n"
            f"Please add at least one button before activating the channel.\n\n"
            f"Use 'Manage Buttons' to add buttons.",
            reply_markup=channel_management_keyboard(channel)
        )
        return

    # Check bot permissions
    has_permissions, error_msg = await check_bot_permissions(context, channel_id)
    if not has_permissions:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} *Permission Error*\n\n{error_msg}",
            reply_markup=channel_management_keyboard(channel)
        )
        return

    channel.status = ChannelStatus.ACTIVE
    success = await db_manager.update_channel(channel)

    if success:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.SUCCESS} *Channel Activated*\n\n"
            f"Buttons will now be added to all new posts in '{channel.channel_name}'.",
            reply_markup=channel_management_keyboard(channel)
        )
    else:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Failed to activate channel.",
            reply_markup=channel_management_keyboard(channel)
        )

# Conversation handler removed - using streamlined auto-detection approach

@user_required
async def pause_channel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel pause"""
    query = update.callback_query
    channel_id = query.data.split('_', 1)[1]
    user_id = update.effective_user.id

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return

    channel.status = ChannelStatus.PAUSED
    success = await db_manager.update_channel(channel)

    if success:
        await safe_edit_message(
            update,
            f"{Emojis.SUCCESS} *Channel Paused*\n\n"
            f"Button addition is temporarily disabled for '{channel.channel_name}'.\n"
            f"Your settings are preserved.",
            reply_markup=channel_management_keyboard(channel)
        )
    else:
        await safe_edit_message(
            update,
            f"{Emojis.ERROR} Failed to pause channel.",
            reply_markup=channel_management_keyboard(channel)
        )

@user_required
async def disable_channel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel disable"""
    query = update.callback_query
    channel_id = query.data.split('_', 1)[1]
    user_id = update.effective_user.id

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return

    channel.status = ChannelStatus.DISABLED
    success = await db_manager.update_channel(channel)

    if success:
        await safe_edit_message(
            update,
            f"{Emojis.SUCCESS} *Channel Disabled*\n\n"
            f"Button addition is disabled for '{channel.channel_name}'.\n"
            f"Your settings are preserved.",
            reply_markup=channel_management_keyboard(channel)
        )
    else:
        await safe_edit_message(
            update,
            f"{Emojis.ERROR} Failed to disable channel.",
            reply_markup=channel_management_keyboard(channel)
        )

@user_required
async def pause_channel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel pause"""
    user_id = update.effective_user.id
    channel_id = context.user_data.get('selected_channel_id')

    if not channel_id:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} No channel selected. Please select a channel first.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel.status = ChannelStatus.PAUSED
    success = await db_manager.update_channel(channel)

    if success:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.SUCCESS} *Channel Paused*\n\n"
            f"Button addition is paused for '{channel.channel_name}'.\n"
            f"Your settings are preserved.",
            reply_markup=channel_management_keyboard(channel)
        )
    else:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Failed to pause channel.",
            reply_markup=channel_management_keyboard(channel)
        )

@user_required
async def disable_channel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel disable"""
    user_id = update.effective_user.id
    channel_id = context.user_data.get('selected_channel_id')

    if not channel_id:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} No channel selected. Please select a channel first.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel.status = ChannelStatus.DISABLED
    success = await db_manager.update_channel(channel)

    if success:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.SUCCESS} *Channel Disabled*\n\n"
            f"Button addition is disabled for '{channel.channel_name}'.\n"
            f"Your settings are preserved.",
            reply_markup=channel_management_keyboard(channel)
        )
    else:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Failed to disable channel.",
            reply_markup=channel_management_keyboard(channel)
        )

# Handler registration
def register_channel_handlers(application):
    """Register channel-related handlers"""
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.ADD} Add Channel$"), add_channel_handler))
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.LIST} My Channels$"), my_channels_handler))
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.ROBOT} Open Admin Link$"), open_admin_link_handler))

    # Channel selection handler (matches status emoji + channel name pattern)
    application.add_handler(MessageHandler(filters.Regex(f"^[{Emojis.ACTIVE}{Emojis.PAUSED}{Emojis.DISABLED}] "), channel_selection_handler))

    # Channel management handlers
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.ACTIVE} (Enable|Resume)$"), activate_channel_handler))
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.PAUSED} Pause$"), pause_channel_handler))
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.DISABLED} Disable$"), disable_channel_handler))

    # Navigation handlers
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.BACK} (Previous|Back to Channels)$"), my_channels_handler))
    application.add_handler(MessageHandler(filters.Regex(f"^Next {Emojis.FORWARD}$"), my_channels_handler))
