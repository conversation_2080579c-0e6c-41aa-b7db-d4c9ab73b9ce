"""
Keyboard utilities for ButtonBot
Generates reply keyboards for bot interface
"""

from typing import List, Optional
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

from config import Emojis
from database.models import Channel, Button, ChannelStatus

def main_menu_keyboard() -> InlineKeyboardMarkup:
    """Generate main menu inline keyboard (2x2 grid)"""
    keyboard = [
        [
            InlineKeyboardButton("ADD CHANNEL", callback_data="add_channel"),
            InlineKeyboardButton("MY CHANNELS", callback_data="my_channels")
        ],
        [
            InlineKeyboardButton("SETTINGS", callback_data="settings"),
            InlineKeyboardButton("HELP", callback_data="help")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

# Old reply keyboard functions removed - now using inline keyboards

# More old reply keyboard functions removed

def generate_post_buttons(buttons: List[Button], max_per_row: int = 3) -> Optional[InlineKeyboardMarkup]:
    """Generate buttons for channel posts"""
    if not buttons:
        return None
    
    # Sort buttons by row and position
    sorted_buttons = sorted(buttons, key=lambda b: (b.row, b.position))
    
    keyboard = []
    current_row = []
    current_row_num = 0
    
    for button in sorted_buttons:
        # Start new row if needed
        if button.row != current_row_num or len(current_row) >= max_per_row:
            if current_row:
                keyboard.append(current_row)
            current_row = []
            current_row_num = button.row
        
        current_row.append(InlineKeyboardButton(button.text, url=button.url))
    
    # Add the last row
    if current_row:
        keyboard.append(current_row)
    
    return InlineKeyboardMarkup(keyboard) if keyboard else None

# Old back_to_main_keyboard function removed

def add_channel_verification_keyboard() -> InlineKeyboardMarkup:
    """Generate keyboard for channel verification failure"""
    keyboard = [
        [
            InlineKeyboardButton("How to make bot admin", url="https://t.me/titanium_bots_channel"),
            InlineKeyboardButton("Recheck", callback_data="recheck_admin")
        ]
    ]
    return InlineKeyboardMarkup(keyboard)

def my_channels_keyboard(channels: List[Channel]) -> InlineKeyboardMarkup:
    """Generate inline keyboard for MY CHANNELS with Remove buttons"""
    keyboard = []

    for channel in channels:
        status_emoji = {
            ChannelStatus.ACTIVE: Emojis.ACTIVE,
            ChannelStatus.PAUSED: Emojis.PAUSED,
            ChannelStatus.DISABLED: Emojis.DISABLED
        }.get(channel.status, Emojis.DISABLED)

        channel_name = channel.channel_name[:20] + "..." if len(channel.channel_name) > 20 else channel.channel_name

        keyboard.append([
            InlineKeyboardButton(f"{status_emoji} {channel_name}", callback_data=f"channel_info_{channel.channel_id}"),
            InlineKeyboardButton("Remove", callback_data=f"remove_channel_{channel.channel_id}")
        ])

    # Add back button
    keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")])

    return InlineKeyboardMarkup(keyboard)

def settings_channels_keyboard(channels: List[Channel]) -> InlineKeyboardMarkup:
    """Generate inline keyboard for SETTINGS with Settings buttons above each channel"""
    keyboard = []

    for channel in channels:
        status_emoji = {
            ChannelStatus.ACTIVE: Emojis.ACTIVE,
            ChannelStatus.PAUSED: Emojis.PAUSED,
            ChannelStatus.DISABLED: Emojis.DISABLED
        }.get(channel.status, Emojis.DISABLED)

        channel_name = channel.channel_name[:25] + "..." if len(channel.channel_name) > 25 else channel.channel_name

        # Settings button above channel name
        keyboard.append([
            InlineKeyboardButton(f"⚙️ Settings", callback_data=f"channel_settings_{channel.channel_id}")
        ])
        keyboard.append([
            InlineKeyboardButton(f"{status_emoji} {channel_name}", callback_data=f"channel_info_{channel.channel_id}")
        ])

    # Add back button
    keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")])

    return InlineKeyboardMarkup(keyboard)

# Old reply keyboard functions and helper functions removed
