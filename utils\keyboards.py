"""
Keyboard utilities for ButtonBot
Generates reply keyboards for bot interface
"""

from typing import List, Optional
from telegram import KeyboardButton, ReplyKeyboardMarkup, InlineKeyboardButton, InlineKeyboardMarkup

from config import Emojis
from database.models import Channel, But<PERSON>, ChannelStatus

def main_menu_keyboard() -> ReplyKeyboardMarkup:
    """Generate main menu keyboard"""
    keyboard = [
        [
            KeyboardButton(f"{Emojis.ADD} Add Channel"),
            KeyboardButton(f"{Emojis.LIST} My Channels")
        ],
        [
            KeyboardButton(f"{Emojis.HELP} Help")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def channels_list_keyboard(channels: List[Channel], page: int = 0, per_page: int = 3) -> ReplyKeyboardMarkup:
    """Generate channels list keyboard with pagination"""
    keyboard = []

    # Calculate pagination
    start_idx = page * per_page
    end_idx = start_idx + per_page
    page_channels = channels[start_idx:end_idx]

    # Add channel buttons (limit to 3 per page for reply keyboard)
    for channel in page_channels:
        status_emoji = {
            ChannelStatus.ACTIVE: Emojis.ACTIVE,
            ChannelStatus.PAUSED: Emojis.PAUSED,
            ChannelStatus.DISABLED: Emojis.DISABLED
        }.get(channel.status, Emojis.DISABLED)

        channel_name = channel.channel_name[:15] + "..." if len(channel.channel_name) > 15 else channel.channel_name
        button_text = f"{status_emoji} {channel_name}"

        keyboard.append([KeyboardButton(button_text)])

    # Add navigation row
    nav_row = []
    if page > 0:
        nav_row.append(KeyboardButton(f"{Emojis.BACK} Previous"))
    if end_idx < len(channels):
        nav_row.append(KeyboardButton(f"Next {Emojis.FORWARD}"))
    if nav_row:
        keyboard.append(nav_row)

    # Add control buttons
    keyboard.append([
        KeyboardButton(f"{Emojis.ADD} Add Channel"),
        KeyboardButton(f"{Emojis.HOME} Main Menu")
    ])

    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def channel_management_keyboard(channel: Channel) -> ReplyKeyboardMarkup:
    """Generate channel management keyboard"""
    keyboard = []

    # Status control buttons
    if channel.status == ChannelStatus.ACTIVE:
        keyboard.append([
            KeyboardButton(f"{Emojis.PAUSED} Pause"),
            KeyboardButton(f"{Emojis.DISABLED} Disable")
        ])
    elif channel.status == ChannelStatus.PAUSED:
        keyboard.append([
            KeyboardButton(f"{Emojis.ACTIVE} Resume"),
            KeyboardButton(f"{Emojis.DISABLED} Disable")
        ])
    else:  # DISABLED
        keyboard.append([
            KeyboardButton(f"{Emojis.ACTIVE} Enable")
        ])

    # Management buttons
    keyboard.extend([
        [
            KeyboardButton(f"{Emojis.BUTTON} Manage Buttons"),
            KeyboardButton(f"{Emojis.SETTINGS} Channel Settings")
        ],
        [
            KeyboardButton(f"{Emojis.DELETE} Remove Channel"),
            KeyboardButton(f"{Emojis.BACK} Back to Channels")
        ]
    ])

    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def button_management_keyboard(channel: Channel) -> ReplyKeyboardMarkup:
    """Generate button management keyboard"""
    keyboard = []

    # Show existing buttons (limit display for reply keyboard)
    for i, button in enumerate(channel.buttons[:5]):  # Show max 5 buttons
        button_text = button.text[:12] + "..." if len(button.text) > 12 else button.text
        keyboard.append([
            KeyboardButton(f"{Emojis.EDIT} {button_text}"),
            KeyboardButton(f"{Emojis.DELETE} Del {i+1}")
        ])

    # Add control buttons
    keyboard.extend([
        [KeyboardButton(f"{Emojis.ADD} Add Button")],
        [
            KeyboardButton(f"{Emojis.SETTINGS} Layout Settings"),
            KeyboardButton(f"{Emojis.INFO} Preview Buttons")
        ],
        [KeyboardButton(f"{Emojis.BACK} Back to Channel")]
    ])

    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def layout_settings_keyboard(channel: Channel) -> ReplyKeyboardMarkup:
    """Generate layout settings keyboard"""
    keyboard = []

    # Buttons per row options
    for i in range(1, 6):
        emoji = Emojis.ACTIVE if channel.max_buttons_per_row == i else Emojis.BUTTON
        keyboard.append([
            KeyboardButton(f"{emoji} {i} button{'s' if i > 1 else ''} per row")
        ])

    keyboard.append([
        KeyboardButton(f"{Emojis.BACK} Back to Buttons")
    ])

    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, persistent=True)

def confirmation_keyboard(action: str, channel_id: str, extra_data: str = "") -> ReplyKeyboardMarkup:
    """Generate confirmation keyboard"""
    keyboard = [
        [
            KeyboardButton(f"{Emojis.SUCCESS} Yes"),
            KeyboardButton(f"{Emojis.CANCEL} No")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def confirmation_keyboard(action: str, channel_id: str, extra_data: str = "") -> ReplyKeyboardMarkup:
    """Generate confirmation keyboard"""
    keyboard = [
        [
            KeyboardButton(f"{Emojis.SUCCESS} Yes"),
            KeyboardButton(f"{Emojis.CANCEL} No")
        ]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def generate_post_buttons(buttons: List[Button], max_per_row: int = 3) -> Optional[InlineKeyboardMarkup]:
    """Generate buttons for channel posts"""
    if not buttons:
        return None
    
    # Sort buttons by row and position
    sorted_buttons = sorted(buttons, key=lambda b: (b.row, b.position))
    
    keyboard = []
    current_row = []
    current_row_num = 0
    
    for button in sorted_buttons:
        # Start new row if needed
        if button.row != current_row_num or len(current_row) >= max_per_row:
            if current_row:
                keyboard.append(current_row)
            current_row = []
            current_row_num = button.row
        
        current_row.append(InlineKeyboardButton(button.text, url=button.url))
    
    # Add the last row
    if current_row:
        keyboard.append(current_row)
    
    return InlineKeyboardMarkup(keyboard) if keyboard else None

def back_to_main_keyboard() -> ReplyKeyboardMarkup:
    """Generate simple back to main menu keyboard"""
    keyboard = [[KeyboardButton(f"{Emojis.HOME} Main Menu")]]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def add_channel_keyboard(bot_username: str) -> ReplyKeyboardMarkup:
    """Generate add channel keyboard with instructions"""
    keyboard = [
        [KeyboardButton(f"{Emojis.ROBOT} Open Admin Link")],
        [KeyboardButton(f"{Emojis.LIST} View All Channels")],
        [KeyboardButton(f"{Emojis.HOME} Main Menu")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def channel_added_keyboard() -> ReplyKeyboardMarkup:
    """Generate keyboard for successful channel addition"""
    keyboard = [
        [KeyboardButton(f"{Emojis.LIST} View All Channels")],
        [KeyboardButton(f"{Emojis.HOME} Main Menu")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

def button_input_keyboard() -> ReplyKeyboardMarkup:
    """Generate keyboard for button input process"""
    keyboard = [
        [KeyboardButton(f"{Emojis.CANCEL} Cancel")]
    ]
    return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

# Helper function to get admin URL for add channel
def get_admin_url(bot_username: str) -> str:
    """Get the admin URL for adding bot to channel"""
    return f"https://t.me/{bot_username}?startchannel=&admin=post_messages+edit_messages+promote_members+delete_messages+restrict_members+invite_users+pin_messages+manage_video_chats+change_info"
