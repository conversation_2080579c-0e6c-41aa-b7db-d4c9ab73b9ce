"""
Start command handler for ButtonBot
Handles /start command and main menu navigation
"""

import logging
from telegram import Update, InputFile, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler, MessageHandler, filters

from config import Emojis, Config
from utils.helpers import user_required, safe_edit_message, safe_send_message, safe_send_photo_message
from utils.keyboards import main_menu_keyboard, my_channels_keyboard, settings_channels_keyboard, add_channel_verification_keyboard
from database.operations import db_manager

logger = logging.getLogger(__name__)

@user_required
async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command - sends start.jpg with new caption and inline keyboard"""

    caption = "I CAN ADD BUTTONS BELOW CHANNEL POSTS.😎"

    # Send start.jpg with caption and 2x2 inline keyboard
    await safe_send_photo_message(
        update, context, caption,
        reply_markup=main_menu_keyboard()
    )

@user_required
async def main_menu_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle main menu callback"""
    query = update.callback_query
    await query.answer()

    caption = "I CAN ADD BUTTONS BELOW CHANNEL POSTS.😎"

    # Edit message to show main menu
    await safe_edit_message(
        update, context, caption,
        reply_markup=main_menu_keyboard()
    )

@user_required
async def add_channel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle ADD CHANNEL button press"""
    query = update.callback_query
    await query.answer()

    bot_username = Config.BOT_USERNAME
    message = f"Please make the bot @{bot_username} admin in your channel then forward a post from your channel or send channel id"

    # Set flag to indicate we're waiting for channel input
    context.user_data['waiting_for_channel_input'] = True

    # Edit message to show add channel instructions
    await safe_edit_message(
        update, context, message,
        reply_markup=None  # No keyboard, waiting for user input
    )

@user_required
async def my_channels_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle MY CHANNELS button press"""
    query = update.callback_query
    await query.answer()

    user_id = update.effective_user.id
    channels = await db_manager.get_user_channels(user_id)

    if not channels:
        message = "You haven't added any channels yet. Use ADD CHANNEL to get started!"
        keyboard = main_menu_keyboard()
    else:
        message = "Here are your channels. Click Remove to delete a channel:"
        keyboard = my_channels_keyboard(channels)

    # Edit message to show channels list
    await safe_edit_message(
        update, context, message,
        reply_markup=keyboard
    )

@user_required
async def settings_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle SETTINGS button press"""
    query = update.callback_query
    await query.answer()

    user_id = update.effective_user.id
    channels = await db_manager.get_user_channels(user_id)

    if not channels:
        message = "You haven't added any channels yet. Use ADD CHANNEL to get started!"
        keyboard = main_menu_keyboard()
    else:
        message = "Select a channel to configure its settings:"
        keyboard = settings_channels_keyboard(channels)

    # Edit message to show settings
    await safe_edit_message(
        update, context, message,
        reply_markup=keyboard
    )

@user_required
async def help_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle HELP button press"""
    query = update.callback_query
    await query.answer()

    help_text = f"""
{Emojis.HELP} *ButtonBot Help & Documentation*

{Emojis.CHANNEL} *Adding Channels:*
1. Make sure you're an admin of the channel
2. Add @{Config.BOT_USERNAME} as administrator
3. Give the bot permission to edit and post messages
4. Use "ADD CHANNEL" and follow the instructions

{Emojis.BUTTON} *Managing Buttons:*
• Add up to 5 buttons per row
• Support for Telegram links (t.me/username)
• Support for external websites
• Customize button text (max 64 characters)
• Preview buttons before saving

{Emojis.SETTINGS} *Channel Settings:*
• *Active*: Buttons added to all new posts
• *Paused*: Temporarily disabled, settings preserved
• *Disabled*: Completely turned off

{Emojis.INFO} *Supported URL Formats:*
• `https://example.com` - External websites
• `https://t.me/username` - Telegram channels/bots
• `https://t.me/joinchat/...` - Telegram invite links
• `tg://resolve?domain=username` - Telegram deep links

{Emojis.WARNING} *Important Notes:*
• Bot must be admin with edit/post permissions
• Buttons are added only to NEW posts
• Each channel has independent settings
• Maximum 10 channels per user

{Emojis.ERROR} *Troubleshooting:*
• If buttons don't appear, check bot permissions
• Ensure channel is set to "Active" status
• Verify button URLs are valid
• Contact support if issues persist

Need more help? Contact @YourSupportUsername
"""

    # Edit message to show help
    await safe_edit_message(
        update, context, help_text,
        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")]])
    )

@user_required
async def remove_channel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle remove channel button press"""
    query = update.callback_query
    await query.answer()

    # Extract channel_id from callback data
    channel_id = query.data.replace("remove_channel_", "")
    user_id = update.effective_user.id

    # Get channel info
    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(
            update, context, "Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Remove the channel
    success = await db_manager.delete_channel(user_id, channel_id)

    if success:
        message = f"Channel '{channel.channel_name}' has been removed successfully."
    else:
        message = "Failed to remove channel. Please try again."

    # Show updated channels list
    channels = await db_manager.get_user_channels(user_id)
    if not channels:
        message += "\n\nYou haven't added any channels yet. Use ADD CHANNEL to get started!"
        keyboard = main_menu_keyboard()
    else:
        message += "\n\nHere are your remaining channels:"
        keyboard = my_channels_keyboard(channels)

    await safe_edit_message(
        update, context, message,
        reply_markup=keyboard
    )

@user_required
async def recheck_admin_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle recheck admin button press"""
    query = update.callback_query
    await query.answer()

    # Get the channel info from context
    channel_data = context.user_data.get('pending_channel')
    if not channel_data:
        await safe_edit_message(
            update, context,
            "No pending channel found. Please try adding the channel again.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Verify bot admin permissions
    try:
        bot_member = await context.bot.get_chat_member(channel_data['channel_id'], context.bot.id)
        if bot_member.status in ['administrator', 'creator']:
            # Bot is admin, proceed with adding channel
            await add_channel_to_database(update, context, channel_data)
        else:
            # Still not admin
            await safe_edit_message(
                update, context,
                "You have not made the bot admin",
                reply_markup=add_channel_verification_keyboard()
            )
    except Exception as e:
        logger.error(f"Error checking bot admin status: {e}")
        await safe_edit_message(
            update, context,
            "Error checking admin status. Please try again.",
            reply_markup=add_channel_verification_keyboard()
        )

async def add_channel_to_database(update: Update, context: ContextTypes.DEFAULT_TYPE, channel_data: dict):
    """Add channel to database after verification"""
    user_id = update.effective_user.id

    # Check if channel already exists
    existing_channel = await db_manager.get_channel(user_id, channel_data['channel_id'])
    if existing_channel:
        await safe_edit_message(
            update, context,
            "This channel is already added to your account.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Check channel limit
    user_channels = await db_manager.get_user_channels(user_id)
    if len(user_channels) >= Config.MAX_CHANNELS_PER_USER:
        await safe_edit_message(
            update, context,
            f"Channel limit reached. You can add up to {Config.MAX_CHANNELS_PER_USER} channels.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Create new channel
    new_channel = Channel(
        channel_id=channel_data['channel_id'],
        channel_name=channel_data['channel_name'],
        channel_username=channel_data.get('channel_username'),
        user_id=user_id,
        status=ChannelStatus.DISABLED
    )

    success = await db_manager.create_channel(new_channel)

    if success:
        await safe_edit_message(
            update, context,
            "Channel added successfully",
            reply_markup=main_menu_keyboard()
        )
        # Clear pending channel data
        context.user_data.pop('pending_channel', None)
    else:
        await safe_edit_message(
            update, context,
            "Failed to add channel. Please try again.",
            reply_markup=main_menu_keyboard()
        )

@user_required
async def process_channel_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Process forwarded message or channel ID input"""
    user_id = update.effective_user.id

    # Check if user is in ADD CHANNEL flow (we can track this with user_data)
    if not context.user_data.get('waiting_for_channel_input'):
        return  # Not in add channel flow

    channel_data = None

    # Check if it's a forwarded message from a channel
    if update.message.forward_from_chat and update.message.forward_from_chat.type == 'channel':
        chat = update.message.forward_from_chat
        channel_data = {
            'channel_id': str(chat.id),
            'channel_name': chat.title or "Unknown Channel",
            'channel_username': chat.username
        }

    # Check if it's a channel ID (text starting with -100)
    elif update.message.text and update.message.text.strip().startswith('-100'):
        channel_id = update.message.text.strip()
        try:
            # Try to get channel info
            chat = await context.bot.get_chat(channel_id)
            if chat.type == 'channel':
                channel_data = {
                    'channel_id': str(chat.id),
                    'channel_name': chat.title or "Unknown Channel",
                    'channel_username': chat.username
                }
        except Exception as e:
            logger.error(f"Error getting channel info for {channel_id}: {e}")
            await safe_send_message(
                update, context,
                "Invalid channel ID or bot doesn't have access to this channel. Please make sure the bot is added as admin first."
            )
            return

    # Check if it's a channel username (starting with @)
    elif update.message.text and update.message.text.strip().startswith('@'):
        username = update.message.text.strip()[1:]  # Remove @
        try:
            chat = await context.bot.get_chat(f"@{username}")
            if chat.type == 'channel':
                channel_data = {
                    'channel_id': str(chat.id),
                    'channel_name': chat.title or "Unknown Channel",
                    'channel_username': chat.username
                }
        except Exception as e:
            logger.error(f"Error getting channel info for @{username}: {e}")
            await safe_send_message(
                update, context,
                "Invalid channel username or bot doesn't have access to this channel. Please make sure the bot is added as admin first."
            )
            return

    if not channel_data:
        await safe_send_message(
            update, context,
            "Please forward a post from your channel or send a valid channel ID/username."
        )
        return

    # Store channel data for verification
    context.user_data['pending_channel'] = channel_data
    context.user_data['waiting_for_channel_input'] = False

    # Verify bot admin permissions
    try:
        bot_member = await context.bot.get_chat_member(channel_data['channel_id'], context.bot.id)
        if bot_member.status in ['administrator', 'creator']:
            # Bot is admin, proceed with adding channel
            await add_channel_to_database(update, context, channel_data)
        else:
            # Bot is not admin
            await safe_send_message(
                update, context,
                "You have not made the bot admin",
                reply_markup=add_channel_verification_keyboard()
            )
    except Exception as e:
        logger.error(f"Error checking bot admin status: {e}")
        await safe_send_message(
            update, context,
            "You have not made the bot admin",
            reply_markup=add_channel_verification_keyboard()
        )

@user_required
async def channel_settings_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel settings button press"""
    query = update.callback_query
    await query.answer()

    # Extract channel_id from callback data
    channel_id = query.data.replace("channel_settings_", "")
    user_id = update.effective_user.id

    # Get channel info
    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(
            update, context, "Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    # For now, redirect to the existing settings handler
    # This would typically show button management, layout settings, etc.
    from utils.keyboards import button_management_keyboard

    settings_message = f"""
⚙️ *Settings for {channel.channel_name}*

Current Status: {channel.status.value.title()}
Buttons: {len(channel.buttons)} configured
Layout: {channel.max_buttons_per_row} buttons per row

Use the existing settings interface to configure this channel.
"""

    await safe_edit_message(
        update, context, settings_message,
        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 Back to Settings", callback_data="settings")]])
    )

@user_required
async def channel_info_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel info button press"""
    query = update.callback_query
    await query.answer()

    # Extract channel_id from callback data
    channel_id = query.data.replace("channel_info_", "")
    user_id = update.effective_user.id

    # Get channel info
    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(
            update, context, "Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Show detailed channel information
    from utils.helpers import format_channel_info

    info_message = f"""
📢 *Channel Information*

{format_channel_info(channel)}

Use the buttons below to manage this channel.
"""

    # Create management keyboard
    keyboard = [
        [InlineKeyboardButton("⚙️ Settings", callback_data=f"channel_settings_{channel_id}")],
        [InlineKeyboardButton("🗑️ Remove Channel", callback_data=f"remove_channel_{channel_id}")],
        [InlineKeyboardButton("🔙 Back to Channels", callback_data="my_channels")]
    ]

    await safe_edit_message(
        update, context, info_message,
        reply_markup=InlineKeyboardMarkup(keyboard)
    )

# Handler registration
def register_start_handlers(application):
    """Register start-related handlers"""
    application.add_handler(CommandHandler("start", start_command))

    # Callback query handlers for inline keyboards
    application.add_handler(CallbackQueryHandler(main_menu_callback, pattern="^main_menu$"))
    application.add_handler(CallbackQueryHandler(add_channel_callback, pattern="^add_channel$"))
    application.add_handler(CallbackQueryHandler(my_channels_callback, pattern="^my_channels$"))
    application.add_handler(CallbackQueryHandler(settings_callback, pattern="^settings$"))
    application.add_handler(CallbackQueryHandler(help_callback, pattern="^help$"))
    application.add_handler(CallbackQueryHandler(remove_channel_callback, pattern="^remove_channel_"))
    application.add_handler(CallbackQueryHandler(recheck_admin_callback, pattern="^recheck_admin$"))
    application.add_handler(CallbackQueryHandler(channel_settings_callback, pattern="^channel_settings_"))
    application.add_handler(CallbackQueryHandler(channel_info_callback, pattern="^channel_info_"))

    # Message handler for channel input (forwarded messages or channel IDs)
    application.add_handler(
        MessageHandler(
            (filters.FORWARDED | filters.TEXT) & filters.ChatType.PRIVATE,
            process_channel_input
        ),
        group=1  # Higher priority than fallback handlers
    )
