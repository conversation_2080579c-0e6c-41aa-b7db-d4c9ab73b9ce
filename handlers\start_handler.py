"""
Start command handler for ButtonBot
Handles /start command and main menu navigation
"""

import logging
from telegram import Update, InputFile
from telegram.ext import ContextTypes, CommandHandler, MessageHandler, filters

from config import Emojis
from utils.helpers import user_required, safe_edit_message, safe_send_message, safe_send_photo_message
from utils.keyboards import main_menu_keyboard, back_to_main_keyboard

logger = logging.getLogger(__name__)

@user_required
async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command - sends single message with image and caption"""
    user = update.effective_user

    welcome_text = f"""
{Emojis.ROBOT} *Welcome to ButtonBot!*

Hello {user.first_name}! I'm here to help you automatically add custom buttons to your Telegram channel posts.

{Emojis.CHANNEL} *What I can do:*
• Add custom buttons to all new posts in your channels
• Support multiple channels with independent settings
• Customize button text, URLs, and layout
• Pause/resume button addition anytime
• Easy-to-use interface with visual controls

{Emojis.SETTINGS} *Getting Started:*
1. Add me as an administrator to your channel
2. Use "Add Channel" to register your channel
3. Configure your custom buttons
4. Enable automatic button addition
5. All new posts will include your buttons!

{Emojis.INFO} *Features:*
• Multi-channel support
• Custom button layouts (1-5 buttons per row)
• Telegram and external URL support
• Real-time status indicators
• Easy pause/resume controls

Choose an option below to get started:
"""

    # Send single message with image and caption
    await safe_send_photo_message(
        update, context, welcome_text,
        reply_markup=main_menu_keyboard()
    )

@user_required
async def main_menu_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle main menu message"""
    main_menu_text = f"""
{Emojis.ROBOT} *ButtonBot Main Menu*

{Emojis.CHANNEL} *Channel Management:*
• Add new channels to the bot
• View and manage your existing channels
• Configure button settings per channel

{Emojis.SETTINGS} *Quick Actions:*
• Access channel settings
• Get help and documentation
• View bot features and capabilities

Select an option below:
"""

    await safe_send_photo_message(
        update, context, main_menu_text,
        reply_markup=main_menu_keyboard()
    )

@user_required
async def help_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle help message"""
    help_text = f"""
{Emojis.HELP} *ButtonBot Help & Documentation*

{Emojis.CHANNEL} *Adding Channels:*
1. Make sure you're an admin of the channel
2. Add @{context.bot.username} as administrator
3. Give the bot permission to edit and post messages
4. Use "Add Channel" and follow the setup wizard

{Emojis.BUTTON} *Managing Buttons:*
• Add up to 5 buttons per row
• Support for Telegram links (t.me/username)
• Support for external websites
• Customize button text (max 64 characters)
• Preview buttons before saving

{Emojis.SETTINGS} *Channel Settings:*
• *Active*: Buttons added to all new posts
• *Paused*: Temporarily disabled, settings preserved
• *Disabled*: Completely turned off

{Emojis.INFO} *Supported URL Formats:*
• `https://example.com` - External websites
• `https://t.me/username` - Telegram channels/bots
• `https://t.me/joinchat/...` - Telegram invite links
• `tg://resolve?domain=username` - Telegram deep links

{Emojis.WARNING} *Important Notes:*
• Bot must be admin with edit/post permissions
• Buttons are added only to NEW posts
• Each channel has independent settings
• Maximum 10 channels per user

{Emojis.ERROR} *Troubleshooting:*
• If buttons don't appear, check bot permissions
• Ensure channel is set to "Active" status
• Verify button URLs are valid
• Contact support if issues persist

Need more help? Contact @YourSupportUsername
"""

    await safe_send_photo_message(
        update, context, help_text,
        reply_markup=back_to_main_keyboard()
    )

# Handler registration
def register_start_handlers(application):
    """Register start-related handlers"""
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.HOME} Main Menu$"), main_menu_handler))
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.HELP} Help$"), help_handler))
