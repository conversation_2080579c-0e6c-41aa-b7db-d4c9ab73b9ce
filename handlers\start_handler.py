"""
Start command handler for ButtonBot
Handles /start command and main menu navigation
"""

import logging
from telegram import Update, InputFile, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, CommandHandler, CallbackQueryHandler, MessageHandler, filters

from config import Emojis, Config
from utils.helpers import user_required, safe_edit_message, safe_send_message, safe_send_photo_message
from utils.keyboards import main_menu_keyboard, my_channels_keyboard, settings_channels_keyboard, add_channel_verification_keyboard, back_to_main_keyboard, channel_management_keyboard, remove_channel_keyboard
from database.operations import db_manager

logger = logging.getLogger(__name__)

@user_required
async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle /start command - sends start.jpg with new caption and inline keyboard"""

    caption = "I CAN ADD BUTTONS BELOW CHANNEL POSTS.😎"

    # Send start.jpg with caption and 2x2 inline keyboard
    await safe_send_photo_message(
        update, context, caption,
        reply_markup=main_menu_keyboard()
    )

@user_required
async def main_menu_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle main menu message"""
    caption = "I CAN ADD BUTTONS BELOW CHANNEL POSTS.😎"

    # Send message with main menu
    await safe_send_photo_message(
        update, context, caption,
        reply_markup=main_menu_keyboard()
    )

@user_required
async def add_channel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle ADD CHANNEL button press"""
    bot_username = Config.BOT_USERNAME
    message = f"Please make the bot @{bot_username} admin in your channel then forward a post from your channel or send channel id"

    # Set flag to indicate we're waiting for channel input
    context.user_data['waiting_for_channel_input'] = True

    # Send message with add channel instructions
    await safe_send_photo_message(
        update, context, message,
        reply_markup=back_to_main_keyboard()
    )

@user_required
async def my_channels_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle MY CHANNELS button press"""
    user_id = update.effective_user.id
    channels = await db_manager.get_user_channels(user_id)

    if not channels:
        message = "You haven't added any channels yet. Use ADD CHANNEL to get started!"
        keyboard = main_menu_keyboard()
    else:
        message = "Here are your channels. Select a channel to manage it:"
        keyboard = my_channels_keyboard(channels)

    # Send message to show channels list
    await safe_send_photo_message(
        update, context, message,
        reply_markup=keyboard
    )

@user_required
async def settings_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle SETTINGS button press"""
    user_id = update.effective_user.id
    channels = await db_manager.get_user_channels(user_id)

    if not channels:
        message = "You haven't added any channels yet. Use ADD CHANNEL to get started!"
        keyboard = main_menu_keyboard()
    else:
        message = "Select a channel to configure its settings:"
        keyboard = settings_channels_keyboard(channels)

    # Send message to show settings
    await safe_send_photo_message(
        update, context, message,
        reply_markup=keyboard
    )

@user_required
async def help_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle HELP button press"""
    help_text = f"""
{Emojis.HELP} *ButtonBot Help & Documentation*

{Emojis.CHANNEL} *Adding Channels:*
1. Make sure you're an admin of the channel
2. Add @{Config.BOT_USERNAME} as administrator
3. Give the bot permission to edit and post messages
4. Use "ADD CHANNEL" and follow the instructions

{Emojis.BUTTON} *Managing Buttons:*
• Add up to 5 buttons per row
• Support for Telegram links (t.me/username)
• Support for external websites
• Customize button text (max 64 characters)
• Preview buttons before saving

{Emojis.SETTINGS} *Channel Settings:*
• *Active*: Buttons added to all new posts
• *Paused*: Temporarily disabled, settings preserved
• *Disabled*: Completely turned off

{Emojis.INFO} *Supported URL Formats:*
• `https://example.com` - External websites
• `https://t.me/username` - Telegram channels/bots
• `https://t.me/joinchat/...` - Telegram invite links
• `tg://resolve?domain=username` - Telegram deep links

{Emojis.WARNING} *Important Notes:*
• Bot must be admin with edit/post permissions
• Buttons are added only to NEW posts
• Each channel has independent settings
• Maximum 10 channels per user

{Emojis.ERROR} *Troubleshooting:*
• If buttons don't appear, check bot permissions
• Ensure channel is set to "Active" status
• Verify button URLs are valid
• Contact support if issues persist

Need more help? Contact @YourSupportUsername
"""

    # Send message to show help
    await safe_send_photo_message(
        update, context, help_text,
        reply_markup=back_to_main_keyboard()
    )

@user_required
async def remove_channel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle Remove Channel button press"""
    user_id = update.effective_user.id
    channels = await db_manager.get_user_channels(user_id)

    if not channels:
        message = "You haven't added any channels yet. Use ADD CHANNEL to get started!"
        keyboard = main_menu_keyboard()
    else:
        message = "Select a channel to remove:"
        keyboard = remove_channel_keyboard(channels)

    await safe_send_photo_message(
        update, context, message,
        reply_markup=keyboard
    )

@user_required
async def channel_selection_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel selection from list"""
    user_id = update.effective_user.id
    message_text = update.message.text

    # Find the channel by matching the button text
    channels = await db_manager.get_user_channels(user_id)
    selected_channel = None

    for channel in channels:
        status_emoji = {
            ChannelStatus.ACTIVE: Emojis.ACTIVE,
            ChannelStatus.PAUSED: Emojis.PAUSED,
            ChannelStatus.DISABLED: Emojis.DISABLED
        }.get(channel.status, Emojis.DISABLED)

        channel_name = channel.channel_name[:15] + "..." if len(channel.channel_name) > 15 else channel.channel_name
        expected_text = f"{status_emoji} {channel_name}"

        if message_text == expected_text:
            selected_channel = channel
            break

    if not selected_channel:
        await safe_send_photo_message(
            update, context,
            "Channel not found. Please select a valid channel.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Store selected channel for future operations
    context.user_data['selected_channel_id'] = selected_channel.channel_id

    # Show channel management options
    from utils.helpers import format_channel_info

    info_message = f"""
📢 *Channel Information*

{format_channel_info(selected_channel)}

Use the buttons below to manage this channel.
"""

    await safe_send_photo_message(
        update, context, info_message,
        reply_markup=channel_management_keyboard(selected_channel)
    )

@user_required
async def how_to_make_admin_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle How to make bot admin button"""
    # Open the help channel
    help_text = f"""
📖 *How to Make Bot Admin*

To add the bot as admin to your channel:

1. Go to your channel
2. Click on channel name at the top
3. Click "Administrators"
4. Click "Add Administrator"
5. Search for @{Config.BOT_USERNAME}
6. Select the bot and give it these permissions:
   • Post Messages
   • Edit Messages
   • Delete Messages

After making the bot admin, come back and use "🔄 Recheck" button.

For detailed instructions with screenshots, visit: https://t.me/titanium_bots_channel
"""

    await safe_send_photo_message(
        update, context, help_text,
        reply_markup=add_channel_verification_keyboard()
    )

@user_required
async def recheck_admin_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle recheck admin button press"""
    # Get the channel info from context
    channel_data = context.user_data.get('pending_channel')
    if not channel_data:
        await safe_send_photo_message(
            update, context,
            "No pending channel found. Please try adding the channel again.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Verify bot admin permissions
    try:
        bot_member = await context.bot.get_chat_member(channel_data['channel_id'], context.bot.id)
        if bot_member.status in ['administrator', 'creator']:
            # Bot is admin, proceed with adding channel
            await add_channel_to_database(update, context, channel_data)
        else:
            # Still not admin
            await safe_send_photo_message(
                update, context,
                "You have not made the bot admin yet. Please follow the instructions and try again.",
                reply_markup=add_channel_verification_keyboard()
            )
    except Exception as e:
        logger.error(f"Error checking bot admin status: {e}")
        await safe_send_photo_message(
            update, context,
            "Error checking admin status. Please try again.",
            reply_markup=add_channel_verification_keyboard()
        )

@user_required
async def remove_specific_channel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle removal of specific channel"""
    user_id = update.effective_user.id
    message_text = update.message.text

    # Extract channel name from button text (remove "🗑️ " prefix)
    if message_text.startswith("🗑️ "):
        channel_name_part = message_text[3:]  # Remove "🗑️ " prefix

        # Find the channel by matching the name
        channels = await db_manager.get_user_channels(user_id)
        selected_channel = None

        for channel in channels:
            short_name = channel.channel_name[:15] + "..." if len(channel.channel_name) > 15 else channel.channel_name
            if short_name == channel_name_part:
                selected_channel = channel
                break

        if selected_channel:
            # Remove the channel
            success = await db_manager.delete_channel(user_id, selected_channel.channel_id)

            if success:
                message = f"Channel '{selected_channel.channel_name}' has been removed successfully."
            else:
                message = "Failed to remove channel. Please try again."

            await safe_send_photo_message(
                update, context, message,
                reply_markup=main_menu_keyboard()
            )
        else:
            await safe_send_photo_message(
                update, context,
                "Channel not found. Please try again.",
                reply_markup=main_menu_keyboard()
            )

@user_required
async def back_to_channels_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle back to channels button"""
    await my_channels_handler(update, context)

# Old callback handlers removed - now using message handlers

# Old callback handler removed

async def add_channel_to_database(update: Update, context: ContextTypes.DEFAULT_TYPE, channel_data: dict):
    """Add channel to database after verification"""
    user_id = update.effective_user.id

    # Check if channel already exists
    existing_channel = await db_manager.get_channel(user_id, channel_data['channel_id'])
    if existing_channel:
        await safe_send_photo_message(
            update, context,
            "This channel is already added to your account.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Check channel limit
    user_channels = await db_manager.get_user_channels(user_id)
    if len(user_channels) >= Config.MAX_CHANNELS_PER_USER:
        await safe_send_photo_message(
            update, context,
            f"Channel limit reached. You can add up to {Config.MAX_CHANNELS_PER_USER} channels.",
            reply_markup=main_menu_keyboard()
        )
        return

    # Create new channel
    from database.models import Channel, ChannelStatus
    new_channel = Channel(
        channel_id=channel_data['channel_id'],
        channel_name=channel_data['channel_name'],
        channel_username=channel_data.get('channel_username'),
        user_id=user_id,
        status=ChannelStatus.DISABLED
    )

    success = await db_manager.create_channel(new_channel)

    if success:
        await safe_send_photo_message(
            update, context,
            "Channel added successfully",
            reply_markup=main_menu_keyboard()
        )
        # Clear pending channel data
        context.user_data.pop('pending_channel', None)
    else:
        await safe_send_photo_message(
            update, context,
            "Failed to add channel. Please try again.",
            reply_markup=main_menu_keyboard()
        )

@user_required
async def process_channel_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Process forwarded message or channel ID input"""
    user_id = update.effective_user.id

    # Check if user is in ADD CHANNEL flow (we can track this with user_data)
    if not context.user_data.get('waiting_for_channel_input'):
        return  # Not in add channel flow

    channel_data = None

    # Check if it's a forwarded message from a channel
    if (hasattr(update.message, 'forward_from_chat') and
        update.message.forward_from_chat and
        update.message.forward_from_chat.type == 'channel'):
        chat = update.message.forward_from_chat
        channel_data = {
            'channel_id': str(chat.id),
            'channel_name': chat.title or "Unknown Channel",
            'channel_username': chat.username
        }

    # Check if it's a channel ID (text starting with -100)
    elif update.message.text and update.message.text.strip().startswith('-100'):
        channel_id = update.message.text.strip()
        try:
            # Try to get channel info
            chat = await context.bot.get_chat(channel_id)
            if chat.type == 'channel':
                channel_data = {
                    'channel_id': str(chat.id),
                    'channel_name': chat.title or "Unknown Channel",
                    'channel_username': chat.username
                }
        except Exception as e:
            logger.error(f"Error getting channel info for {channel_id}: {e}")
            await safe_send_photo_message(
                update, context,
                "Invalid channel ID or bot doesn't have access to this channel. Please make sure the bot is added as admin first.",
                reply_markup=back_to_main_keyboard()
            )
            return

    # Check if it's a channel username (starting with @)
    elif update.message.text and update.message.text.strip().startswith('@'):
        username = update.message.text.strip()[1:]  # Remove @
        try:
            chat = await context.bot.get_chat(f"@{username}")
            if chat.type == 'channel':
                channel_data = {
                    'channel_id': str(chat.id),
                    'channel_name': chat.title or "Unknown Channel",
                    'channel_username': chat.username
                }
        except Exception as e:
            logger.error(f"Error getting channel info for @{username}: {e}")
            await safe_send_photo_message(
                update, context,
                "Invalid channel username or bot doesn't have access to this channel. Please make sure the bot is added as admin first.",
                reply_markup=back_to_main_keyboard()
            )
            return

    if not channel_data:
        await safe_send_photo_message(
            update, context,
            "Please forward a post from your channel or send a valid channel ID/username.",
            reply_markup=back_to_main_keyboard()
        )
        return

    # Store channel data for verification
    context.user_data['pending_channel'] = channel_data
    context.user_data['waiting_for_channel_input'] = False

    # Verify bot admin permissions
    try:
        bot_member = await context.bot.get_chat_member(channel_data['channel_id'], context.bot.id)
        if bot_member.status in ['administrator', 'creator']:
            # Bot is admin, proceed with adding channel
            await add_channel_to_database(update, context, channel_data)
        else:
            # Bot is not admin
            await safe_send_photo_message(
                update, context,
                "You have not made the bot admin",
                reply_markup=add_channel_verification_keyboard()
            )
    except Exception as e:
        logger.error(f"Error checking bot admin status: {e}")
        await safe_send_photo_message(
            update, context,
            "You have not made the bot admin",
            reply_markup=add_channel_verification_keyboard()
        )

# Old callback handlers removed - now using message handlers

# All old callback handlers removed

# Handler registration
def register_start_handlers(application):
    """Register start-related handlers"""
    application.add_handler(CommandHandler("start", start_command))

    # Message handlers for reply keyboard buttons
    application.add_handler(MessageHandler(filters.Regex("^🔙 Back to Main Menu$"), main_menu_handler))
    application.add_handler(MessageHandler(filters.Regex("^ADD CHANNEL$"), add_channel_handler))
    application.add_handler(MessageHandler(filters.Regex("^MY CHANNELS$"), my_channels_handler))
    application.add_handler(MessageHandler(filters.Regex("^SETTINGS$"), settings_handler))
    application.add_handler(MessageHandler(filters.Regex("^HELP$"), help_handler))
    application.add_handler(MessageHandler(filters.Regex("^🗑️ Remove Channel$"), remove_channel_handler))
    application.add_handler(MessageHandler(filters.Regex("^📖 How to make bot admin$"), how_to_make_admin_handler))
    application.add_handler(MessageHandler(filters.Regex("^🔄 Recheck$"), recheck_admin_handler))
    application.add_handler(MessageHandler(filters.Regex("^🔙 Back to Channels$"), back_to_channels_handler))

    # Channel selection handlers (for status emoji + channel name pattern)
    application.add_handler(MessageHandler(filters.Regex(f"^[{Emojis.ACTIVE}{Emojis.PAUSED}{Emojis.DISABLED}] "), channel_selection_handler))

    # Remove specific channel handler (for 🗑️ + channel name pattern)
    application.add_handler(MessageHandler(filters.Regex("^🗑️ "), remove_specific_channel_handler))

    # Message handler for channel input (forwarded messages or channel IDs)
    application.add_handler(
        MessageHandler(
            (filters.FORWARDED | filters.TEXT) & filters.ChatType.PRIVATE,
            process_channel_input
        ),
        group=1  # Higher priority than fallback handlers
    )
