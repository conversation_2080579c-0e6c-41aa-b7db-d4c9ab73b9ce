"""
ButtonBot - Automatic Telegram Channel Button Manager
Main application entry point
"""

import logging
import asyncio
from telegram.ext import Application, CommandHandler

from config import Config, setup_logging
from database.operations import db_manager
from handlers.start_handler import register_start_handlers
from handlers.channel_handler import register_channel_handlers
from handlers.settings_handler import register_settings_handlers
from handlers.message_handler import register_message_handlers

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

async def help_command(update, context):
    """Handle /help command"""
    from handlers.start_handler import help_callback
    await help_callback(update, context)

async def mychannels_command(update, context):
    """Handle /mychannels command"""
    from handlers.channel_handler import my_channels_callback
    await my_channels_callback(update, context)

async def settings_command(update, context):
    """Handle /settings command"""
    from handlers.channel_handler import my_channels_callback
    await my_channels_callback(update, context)

async def addchannel_command(update, context):
    """Handle /addchannel command"""
    from handlers.channel_handler import add_channel_callback
    await add_channel_callback(update, context)

async def error_handler(update, context):
    """Handle errors"""
    logger.error(f"Update {update} caused error {context.error}")
    
    if update and update.effective_chat:
        try:
            await context.bot.send_message(
                chat_id=update.effective_chat.id,
                text="🤖 Sorry, something went wrong. Please try again or use /start to return to the main menu."
            )
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")

def main():
    """Main function to run the bot"""
    try:
        # Validate configuration
        Config.validate()
        logger.info("Configuration validated successfully")
        
        # Create application
        application = Application.builder().token(Config.BOT_TOKEN).build()
        
        # Register handlers
        register_start_handlers(application)
        register_channel_handlers(application)
        register_settings_handlers(application)
        register_message_handlers(application)
        
        # Add command handlers
        application.add_handler(CommandHandler("help", help_command))
        application.add_handler(CommandHandler("mychannels", mychannels_command))
        application.add_handler(CommandHandler("settings", settings_command))
        application.add_handler(CommandHandler("addchannel", addchannel_command))
        
        # Add error handler
        application.add_error_handler(error_handler)
        
        logger.info("ButtonBot started successfully")
        logger.info(f"Bot username: @{Config.BOT_USERNAME}")
        
        # Run the bot
        application.run_polling(
            allowed_updates=['message', 'callback_query', 'channel_post', 'edited_channel_post']
        )
        
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")
        raise
    finally:
        # Close database connection
        db_manager.close()
        logger.info("Bot stopped")

if __name__ == '__main__':
    main()
