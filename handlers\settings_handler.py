"""
Settings and button management handler for ButtonBot
Handles button configuration, layout settings, and previews
"""

import logging
from telegram import Update
from telegram.ext import ContextTypes, CallbackQueryHandler, MessageHandler, filters, ConversationHandler

from config import Emojis
from database.models import <PERSON><PERSON>, ChannelStatus
from database.operations import db_manager
from utils.helpers import (
    user_required, safe_edit_message, safe_send_message, safe_send_photo_message,
    format_button_preview, format_channel_info
)
from utils.keyboards import (
    generate_post_buttons, main_menu_keyboard
)
from utils.validators import validate_button_text, validate_url

logger = logging.getLogger(__name__)

# Conversation states
WAITING_BUTTON_TEXT = 1
WAITING_BUTTON_URL = 2
EDITING_BUTTON_TEXT = 3
EDITING_BUTTON_URL = 4

@user_required
async def button_management_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button management callback"""
    query = update.callback_query
    channel_id = query.data.split('_', 1)[1]
    user_id = update.effective_user.id
    
    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return
    
    button_text = f"""
{Emojis.BUTTON} *Button Management*

Channel: {channel.channel_name}
Current buttons: {len(channel.buttons)}
Layout: {channel.max_buttons_per_row} buttons per row

{format_button_preview(channel)}

{Emojis.SETTINGS} *Management Options:*
Use the buttons below to add, edit, or remove buttons.
"""
    
    await safe_edit_message(
        update, button_text,
        reply_markup=button_management_keyboard(channel)
    )

@user_required
async def add_button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle add button callback"""
    query = update.callback_query
    channel_id = query.data.split('_', 2)[2]
    
    # Store channel ID in context for later use
    context.user_data['current_channel_id'] = channel_id
    context.user_data['button_action'] = 'add'
    
    await safe_edit_message(
        update,
        f"{Emojis.ADD} *Add New Button*\n\n"
        f"Please send the button text (max 64 characters):\n\n"
        f"Examples:\n"
        f"• Visit Website\n"
        f"• Join Channel\n"
        f"• Contact Support\n"
        f"• Download App"
    )
    
    return WAITING_BUTTON_TEXT

@user_required
async def process_button_text(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Process button text input"""
    text = update.message.text.strip()
    
    # Validate button text
    is_valid, error_msg = validate_button_text(text)
    if not is_valid:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} {error_msg}\n\nPlease try again:"
        )
        return WAITING_BUTTON_TEXT
    
    # Store button text
    context.user_data['button_text'] = text
    
    await safe_send_photo_message(
        update, context,
        f"{Emojis.LINK} *Button URL*\n\n"
        f"Button text: {text}\n\n"
        f"Now send the button URL:\n\n"
        f"Supported formats:\n"
        f"• https://example.com\n"
        f"• https://t.me/username\n"
        f"• https://t.me/joinchat/...\n"
        f"• tg://resolve?domain=username"
    )
    
    return WAITING_BUTTON_URL

@user_required
async def process_button_url(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Process button URL input"""
    url = update.message.text.strip()
    
    # Validate URL
    is_valid, error_msg = validate_url(url)
    if not is_valid:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} {error_msg}\n\nPlease try again:"
        )
        return WAITING_BUTTON_URL
    
    # Get channel and add button
    channel_id = context.user_data.get('current_channel_id')
    button_text = context.user_data.get('button_text')
    user_id = update.effective_user.id
    
    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Channel not found.",
        )
        return ConversationHandler.END
    
    # Create new button
    new_button = Button(
        text=button_text,
        url=url if url.startswith(('http://', 'https://', 'tg://')) else f'https://{url}',
        row=len(channel.buttons) // channel.max_buttons_per_row,
        position=len(channel.buttons) % channel.max_buttons_per_row
    )
    
    channel.buttons.append(new_button)
    success = await db_manager.update_channel(channel)
    
    if success:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.SUCCESS} *Button Added Successfully!*\n\n"
            f"Text: {button_text}\n"
            f"URL: {new_button.url}\n\n"
            f"Use /mychannels to manage your buttons.",
            reply_markup=button_management_keyboard(channel)
        )
    else:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Failed to add button. Please try again."
        )
    
    # Clear context data
    context.user_data.clear()
    return ConversationHandler.END

@user_required
async def layout_settings_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle layout settings callback"""
    query = update.callback_query
    channel_id = query.data.split('_', 1)[1]
    user_id = update.effective_user.id
    
    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return
    
    layout_text = f"""
{Emojis.SETTINGS} *Button Layout Settings*

Channel: {channel.channel_name}
Current setting: {channel.max_buttons_per_row} buttons per row

{Emojis.INFO} *Layout Options:*
Choose how many buttons to display per row.
This affects how your buttons are arranged in posts.

Current buttons: {len(channel.buttons)}
"""
    
    await safe_edit_message(
        update, layout_text,
        reply_markup=layout_settings_keyboard(channel)
    )

@user_required
async def set_layout_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle layout setting change"""
    query = update.callback_query
    _, _, channel_id, buttons_per_row = query.data.split('_')
    user_id = update.effective_user.id
    
    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return
    
    # Update layout setting
    channel.max_buttons_per_row = int(buttons_per_row)
    
    # Reorganize existing buttons
    for i, button in enumerate(channel.buttons):
        button.row = i // channel.max_buttons_per_row
        button.position = i % channel.max_buttons_per_row
    
    success = await db_manager.update_channel(channel)
    
    if success:
        await safe_edit_message(
            update,
            f"{Emojis.SUCCESS} *Layout Updated*\n\n"
            f"Buttons per row: {buttons_per_row}\n"
            f"Your existing buttons have been reorganized.",
            reply_markup=layout_settings_keyboard(channel)
        )
    else:
        await safe_edit_message(
            update,
            f"{Emojis.ERROR} Failed to update layout.",
            reply_markup=layout_settings_keyboard(channel)
        )

@user_required
async def preview_buttons_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button preview callback"""
    query = update.callback_query
    channel_id = query.data.split('_', 1)[1]
    user_id = update.effective_user.id
    
    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return
    
    if not channel.buttons:
        await safe_edit_message(
            update,
            f"{Emojis.INFO} *No Buttons to Preview*\n\n"
            f"Add some buttons first to see the preview.",
            reply_markup=button_management_keyboard(channel)
        )
        return
    
    # Generate preview
    preview_text = f"""
{Emojis.INFO} *Button Preview*

This is how your buttons will appear on channel posts:

Channel: {channel.channel_name}
Layout: {channel.max_buttons_per_row} buttons per row
Total buttons: {len(channel.buttons)}

{format_button_preview(channel)}

The actual buttons are shown below:
"""
    
    # Generate actual button markup
    button_markup = generate_post_buttons(channel.buttons, channel.max_buttons_per_row)
    
    await safe_edit_message(
        update, preview_text,
        reply_markup=button_markup
    )
    
    # Send management keyboard in a separate message with image
    await safe_send_photo_message(
        update, context,
        f"{Emojis.SETTINGS} *Button Management*",
        reply_markup=button_management_keyboard(channel)
    )

# Add button conversation
# Note: Using per_message=False because we need MessageHandler for text input
# per_message=True would require all handlers to be CallbackQueryHandler
add_button_conversation = ConversationHandler(
    entry_points=[CallbackQueryHandler(add_button_callback, pattern="^add_button_")],
    states={
        WAITING_BUTTON_TEXT: [MessageHandler(filters.TEXT & ~filters.COMMAND, process_button_text)],
        WAITING_BUTTON_URL: [MessageHandler(filters.TEXT & ~filters.COMMAND, process_button_url)]
    },
    fallbacks=[CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^buttons_")],
    per_message=False  # Must be False when using MessageHandler in states
)

@user_required
async def delete_button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button deletion"""
    query = update.callback_query
    _, _, channel_id, button_index = query.data.split('_')
    user_id = update.effective_user.id

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return

    try:
        button_index = int(button_index)
        if 0 <= button_index < len(channel.buttons):
            deleted_button = channel.buttons.pop(button_index)

            # Reorganize remaining buttons
            for i, button in enumerate(channel.buttons):
                button.row = i // channel.max_buttons_per_row
                button.position = i % channel.max_buttons_per_row

            success = await db_manager.update_channel(channel)

            if success:
                await safe_edit_message(
                    update,
                    f"{Emojis.SUCCESS} *Button Deleted*\n\n"
                    f"Removed: {deleted_button.text}\n"
                    f"Remaining buttons: {len(channel.buttons)}",
                    reply_markup=button_management_keyboard(channel)
                )
            else:
                await safe_edit_message(
                    update,
                    f"{Emojis.ERROR} Failed to delete button.",
                    reply_markup=button_management_keyboard(channel)
                )
        else:
            await safe_edit_message(
                update,
                f"{Emojis.ERROR} Button not found.",
                reply_markup=button_management_keyboard(channel)
            )
    except (ValueError, IndexError):
        await safe_edit_message(
            update,
            f"{Emojis.ERROR} Invalid button selection.",
            reply_markup=button_management_keyboard(channel)
        )

@user_required
async def delete_channel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle channel deletion confirmation"""
    query = update.callback_query
    channel_id = query.data.split('_', 1)[1]
    user_id = update.effective_user.id

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return

    confirm_text = f"""
{Emojis.WARNING} *Confirm Channel Removal*

Are you sure you want to remove this channel?

Channel: {channel.channel_name}
Buttons: {len(channel.buttons)}

{Emojis.ERROR} *Warning:* This action cannot be undone!
All button configurations will be lost.
"""

    await safe_edit_message(
        update, confirm_text,
        reply_markup=confirmation_keyboard("delete_channel", channel_id)
    )

@user_required
async def confirm_delete_channel_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle confirmed channel deletion"""
    query = update.callback_query
    _, _, channel_id = query.data.split('_', 2)
    user_id = update.effective_user.id

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_edit_message(update, f"{Emojis.ERROR} Channel not found.")
        return

    success = await db_manager.delete_channel(user_id, channel_id)

    if success:
        await safe_edit_message(
            update,
            f"{Emojis.SUCCESS} *Channel Removed*\n\n"
            f"'{channel.channel_name}' has been removed from your list.\n"
            f"All button configurations have been deleted.",
            reply_markup=main_menu_keyboard()
        )
    else:
        await safe_edit_message(
            update,
            f"{Emojis.ERROR} Failed to remove channel.",
            reply_markup=channel_management_keyboard(channel)
        )

# New reply keyboard handlers
@user_required
async def button_management_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button management message"""
    user_id = update.effective_user.id
    channel_id = context.user_data.get('selected_channel_id')

    if not channel_id:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} No channel selected. Please select a channel first.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    button_text = f"""
{Emojis.BUTTON} *Button Management*

Channel: {channel.channel_name}
Current buttons: {len(channel.buttons)}
Layout: {channel.max_buttons_per_row} buttons per row

{Emojis.INFO} *Current Buttons:*
"""

    if channel.buttons:
        for i, button in enumerate(channel.buttons):
            button_text += f"\n{i+1}. {button.text} → {button.url[:30]}..."
    else:
        button_text += f"\nNo buttons configured yet."

    button_text += f"\n\nUse the options below to manage buttons:"

    await safe_send_photo_message(
        update, context, button_text,
        reply_markup=button_management_keyboard(channel)
    )

@user_required
async def layout_settings_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle layout settings message"""
    user_id = update.effective_user.id
    channel_id = context.user_data.get('selected_channel_id')

    if not channel_id:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} No channel selected. Please select a channel first.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    layout_text = f"""
{Emojis.SETTINGS} *Button Layout Settings*

Channel: {channel.channel_name}
Current layout: {channel.max_buttons_per_row} buttons per row

{Emojis.INFO} *Choose buttons per row:*
Select how many buttons should appear in each row.
"""

    await safe_send_photo_message(
        update, context, layout_text,
        reply_markup=layout_settings_keyboard(channel)
    )

@user_required
async def back_to_channel_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle back to channel message"""
    user_id = update.effective_user.id
    channel_id = context.user_data.get('selected_channel_id')

    if not channel_id:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} No channel selected. Please select a channel first.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel = await db_manager.get_channel(user_id, channel_id)
    if not channel:
        await safe_send_photo_message(
            update, context,
            f"{Emojis.ERROR} Channel not found.",
            reply_markup=main_menu_keyboard()
        )
        return

    channel_text = f"""
{Emojis.CHANNEL} *Channel Details*

{format_channel_info(channel)}

{Emojis.SETTINGS} *Management Options:*
Use the buttons below to manage this channel.
"""

    await safe_send_photo_message(
        update, context, channel_text,
        reply_markup=channel_management_keyboard(channel)
    )

# Handler registration
def register_settings_handlers(application):
    """Register settings-related handlers"""
    # Keep conversation handler for button input
    application.add_handler(add_button_conversation)

    # Reply keyboard handlers
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.BUTTON} Manage Buttons$"), button_management_handler))
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.SETTINGS} (Layout Settings|Channel Settings)$"), layout_settings_handler))
    application.add_handler(MessageHandler(filters.Regex(f"^{Emojis.BACK} Back to (Channel|Buttons)$"), back_to_channel_handler))

    # Keep existing callback handlers for complex operations
    application.add_handler(CallbackQueryHandler(button_management_callback, pattern="^buttons_"))
    application.add_handler(CallbackQueryHandler(layout_settings_callback, pattern="^layout_"))
    application.add_handler(CallbackQueryHandler(set_layout_callback, pattern="^layout_set_"))
    application.add_handler(CallbackQueryHandler(preview_buttons_callback, pattern="^preview_"))
    application.add_handler(CallbackQueryHandler(delete_button_callback, pattern="^delete_button_"))
    application.add_handler(CallbackQueryHandler(delete_channel_callback, pattern="^delete_"))
    application.add_handler(CallbackQueryHandler(confirm_delete_channel_callback, pattern="^confirm_delete_channel_"))
