"""
Helper utilities for ButtonBot
Common utility functions and decorators
"""

import logging
import functools
from typing import Callable, Any, Optional
from datetime import datetime

from telegram import Update, User as TelegramUser
from telegram.ext import ContextTypes

from config import Emojis
from database.models import User, Channel, ChannelStatus
from database.operations import db_manager

logger = logging.getLogger(__name__)

def user_required(func: Callable) -> Callable:
    """Decorator to ensure user exists in database"""
    @functools.wraps(func)
    async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        if not update.effective_user:
            return
        
        telegram_user = update.effective_user
        
        # Create or update user in database
        user = User(
            user_id=telegram_user.id,
            username=telegram_user.username,
            first_name=telegram_user.first_name,
            last_name=telegram_user.last_name,
            language_code=telegram_user.language_code,
            is_premium=getattr(telegram_user, 'is_premium', False)
        )
        
        await db_manager.create_user(user)
        await db_manager.update_user_activity(telegram_user.id)
        
        return await func(update, context, *args, **kwargs)
    
    return wrapper

def admin_required(func: Callable) -> Callable:
    """Decorator to check if user is admin of the channel"""
    @functools.wraps(func)
    async def wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        # This would need to be implemented based on your admin verification logic
        # For now, we'll assume all users can manage their own channels
        return await func(update, context, *args, **kwargs)
    
    return wrapper

async def check_bot_permissions(context: ContextTypes.DEFAULT_TYPE, channel_id: str) -> tuple[bool, str]:
    """
    Check if bot has necessary permissions in the channel
    Returns (has_permissions, error_message)
    """
    try:
        # Get bot's member status in the channel
        bot_member = await context.bot.get_chat_member(channel_id, context.bot.id)
        
        if bot_member.status not in ['administrator', 'creator']:
            return False, "Bot must be an administrator in the channel"
        
        # Check specific permissions
        if not bot_member.can_edit_messages:
            return False, "Bot needs permission to edit messages"
        
        if not bot_member.can_post_messages:
            return False, "Bot needs permission to post messages"
        
        return True, ""
        
    except Exception as e:
        logger.error(f"Error checking bot permissions for channel {channel_id}: {e}")
        return False, f"Cannot access channel: {str(e)}"

async def get_channel_info(context: ContextTypes.DEFAULT_TYPE, channel_identifier: str) -> tuple[Optional[str], Optional[str], Optional[str]]:
    """
    Get channel information from Telegram
    Returns (channel_id, channel_name, channel_username)
    """
    try:
        chat = await context.bot.get_chat(channel_identifier)
        
        if chat.type not in ['channel', 'supergroup']:
            return None, None, None
        
        return str(chat.id), chat.title, chat.username
        
    except Exception as e:
        logger.error(f"Error getting channel info for {channel_identifier}: {e}")
        return None, None, None

def format_channel_status(status: ChannelStatus) -> str:
    """Format channel status with emoji"""
    status_map = {
        ChannelStatus.ACTIVE: f"{Emojis.ACTIVE} Active",
        ChannelStatus.PAUSED: f"{Emojis.PAUSED} Paused",
        ChannelStatus.DISABLED: f"{Emojis.DISABLED} Disabled"
    }
    return status_map.get(status, f"{Emojis.DISABLED} Unknown")

def format_channel_info(channel: Channel) -> str:
    """Format channel information for display"""
    info = f"{Emojis.CHANNEL} *{channel.channel_name}*\n"
    info += f"Status: {format_channel_status(channel.status)}\n"
    
    if channel.channel_username:
        info += f"Username: @{channel.channel_username}\n"
    
    info += f"Buttons: {len(channel.buttons)}\n"
    info += f"Layout: {channel.max_buttons_per_row} per row\n"
    info += f"Created: {channel.created_at.strftime('%Y-%m-%d')}"
    
    return info

def format_button_preview(channel: Channel) -> str:
    """Format button preview for display"""
    if not channel.buttons:
        return f"{Emojis.INFO} No buttons configured"
    
    preview = f"{Emojis.BUTTON} *Button Preview:*\n\n"
    
    # Group buttons by row
    rows = {}
    for button in channel.buttons:
        if button.row not in rows:
            rows[button.row] = []
        rows[button.row].append(button)
    
    # Sort and display
    for row_num in sorted(rows.keys()):
        row_buttons = sorted(rows[row_num], key=lambda b: b.position)
        button_texts = [f"[{btn.text}]" for btn in row_buttons]
        preview += f"Row {row_num + 1}: {' '.join(button_texts)}\n"
    
    return preview

def truncate_text(text: str, max_length: int = 50) -> str:
    """Truncate text with ellipsis if too long"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def truncate_caption(text: str, max_length: int = 1000) -> str:
    """
    Truncate caption text for Telegram photo captions with safe margin
    Telegram limit is 1024 characters, using 1000 for safety
    """
    if len(text) <= max_length:
        return text

    # Try to truncate at a word boundary near the limit
    truncated = text[:max_length-3]

    # Find the last space to avoid cutting words
    last_space = truncated.rfind(' ')
    if last_space > max_length * 0.8:  # Only use word boundary if it's not too far back
        truncated = truncated[:last_space]

    return truncated + "..."

def normalize_text_for_comparison(text: str) -> str:
    """
    Normalize text for content comparison
    Removes extra whitespace and normalizes line endings
    """
    if not text:
        return ""

    # Normalize whitespace and line endings
    normalized = ' '.join(text.split())
    return normalized.strip()

def escape_markdown(text: str) -> str:
    """Escape markdown special characters"""
    special_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
    for char in special_chars:
        text = text.replace(char, f'\\{char}')
    return text

def format_error_message(error: str) -> str:
    """Format error message with emoji"""
    return f"{Emojis.ERROR} {error}"

def format_success_message(message: str) -> str:
    """Format success message with emoji"""
    return f"{Emojis.SUCCESS} {message}"

def format_warning_message(message: str) -> str:
    """Format warning message with emoji"""
    return f"{Emojis.WARNING} {message}"

def format_info_message(message: str) -> str:
    """Format info message with emoji"""
    return f"{Emojis.INFO} {message}"

async def safe_edit_message(update: Update, text: str, reply_markup=None, parse_mode='Markdown'):
    """Safely edit message caption with error handling (maintains persistent image)"""
    try:
        # Truncate caption if too long
        truncated_text = truncate_caption(text)
        if len(truncated_text) != len(text):
            logger.warning(f"Caption truncated from {len(text)} to {len(truncated_text)} characters")

        if update.callback_query:
            message = update.callback_query.message

            # Check if message has photo
            if message.photo:
                # Check if content is different to avoid "Message is not modified" error
                current_caption = message.caption or ""
                if normalize_text_for_comparison(current_caption) == normalize_text_for_comparison(truncated_text):
                    logger.debug("Skipping edit - caption content is identical")
                    return

                try:
                    await update.callback_query.edit_message_caption(
                        caption=truncated_text,
                        reply_markup=reply_markup,
                        parse_mode=parse_mode
                    )
                except Exception as caption_error:
                    if "Media_caption_too_long" in str(caption_error):
                        # Further truncate if still too long
                        shorter_text = truncate_caption(truncated_text, 800)
                        logger.warning(f"Caption still too long, further truncating to {len(shorter_text)} characters")
                        await update.callback_query.edit_message_caption(
                            caption=shorter_text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode
                        )
                    else:
                        raise caption_error
            else:
                # Fallback to edit text if no photo
                current_text = message.text or ""
                if normalize_text_for_comparison(current_text) == normalize_text_for_comparison(truncated_text):
                    logger.debug("Skipping edit - text content is identical")
                    return

                await update.callback_query.edit_message_text(
                    text=truncated_text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode
                )
        elif update.message:
            # For direct messages, check if it has photo
            if update.message.photo:
                current_caption = update.message.caption or ""
                if normalize_text_for_comparison(current_caption) == normalize_text_for_comparison(truncated_text):
                    logger.debug("Skipping edit - caption content is identical")
                    return

                try:
                    await update.message.edit_caption(
                        caption=truncated_text,
                        reply_markup=reply_markup,
                        parse_mode=parse_mode
                    )
                except Exception as caption_error:
                    if "Media_caption_too_long" in str(caption_error):
                        shorter_text = truncate_caption(truncated_text, 800)
                        logger.warning(f"Caption still too long, further truncating to {len(shorter_text)} characters")
                        await update.message.edit_caption(
                            caption=shorter_text,
                            reply_markup=reply_markup,
                            parse_mode=parse_mode
                        )
                    else:
                        raise caption_error
            else:
                # Fallback to edit text if no photo
                current_text = update.message.text or ""
                if normalize_text_for_comparison(current_text) == normalize_text_for_comparison(truncated_text):
                    logger.debug("Skipping edit - text content is identical")
                    return

                await update.message.edit_text(
                    text=truncated_text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode
                )
    except Exception as e:
        if "Message is not modified" in str(e):
            logger.debug("Message edit skipped - content unchanged")
        else:
            logger.error(f"Error editing message: {e}")

async def safe_send_message(update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, reply_markup=None, parse_mode='Markdown'):
    """Safely send message with error handling"""
    try:
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=text,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
    except Exception as e:
        logger.error(f"Error sending message: {e}")

async def safe_send_photo_message(update: Update, context: ContextTypes.DEFAULT_TYPE, text: str, reply_markup=None, parse_mode='Markdown'):
    """Safely send photo message with caption and error handling"""
    try:
        # Truncate caption if too long
        truncated_text = truncate_caption(text)
        if len(truncated_text) != len(text):
            logger.warning(f"Caption truncated from {len(text)} to {len(truncated_text)} characters")

        with open('start.jpg', 'rb') as photo:
            try:
                await context.bot.send_photo(
                    chat_id=update.effective_chat.id,
                    photo=photo,
                    caption=truncated_text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode
                )
            except Exception as caption_error:
                if "Media_caption_too_long" in str(caption_error):
                    # Further truncate if still too long
                    shorter_text = truncate_caption(truncated_text, 800)
                    logger.warning(f"Caption still too long, further truncating to {len(shorter_text)} characters")
                    photo.seek(0)  # Reset file pointer
                    await context.bot.send_photo(
                        chat_id=update.effective_chat.id,
                        photo=photo,
                        caption=shorter_text,
                        reply_markup=reply_markup,
                        parse_mode=parse_mode
                    )
                else:
                    raise caption_error
    except FileNotFoundError:
        # Fallback to text-only if image not found
        logger.warning("start.jpg not found, falling back to text message")
        await safe_send_message(update, context, truncated_text, reply_markup, parse_mode)
    except Exception as e:
        logger.error(f"Error sending photo message: {e}")
        # Fallback to text-only message
        await safe_send_message(update, context, text, reply_markup, parse_mode)
