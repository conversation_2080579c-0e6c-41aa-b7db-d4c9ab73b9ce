"""
Configuration module for ButtonBot
Handles environment variables and bot settings
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for ButtonBot"""
    
    # Telegram Bot Settings
    BOT_TOKEN = os.getenv('BOT_TOKEN')
    BOT_USERNAME = os.getenv('BOT_USERNAME')
    
    # MongoDB Settings
    MONGODB_URL = os.getenv('MONGODB_URL', 'mongodb://localhost:27017/')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'buttonbot')
    
    # Bot Limits
    MAX_BUTTONS_PER_ROW = int(os.getenv('MAX_BUTTONS_PER_ROW', 5))
    MAX_CHANNELS_PER_USER = int(os.getenv('MAX_CHANNELS_PER_USER', 10))
    
    # Logging Settings
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'buttonbot.log')
    
    @classmethod
    def validate(cls):
        """Validate required configuration"""
        if not cls.BOT_TOKEN:
            raise ValueError("BOT_TOKEN is required")
        if not cls.BOT_USERNAME:
            raise ValueError("BOT_USERNAME is required")
        return True

# Configure logging
def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(Config.LOG_FILE),
            logging.StreamHandler()
        ]
    )

    # Suppress noisy third-party loggers
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('httpcore').setLevel(logging.WARNING)
    logging.getLogger('telegram').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

# Emoji constants for UI
class Emojis:
    """Emoji constants for consistent UI"""
    ROBOT = "🤖"
    CHANNEL = "📢"
    SETTINGS = "⚙️"
    HELP = "❓"
    ADD = "➕"
    EDIT = "✏️"
    DELETE = "🗑️"
    ACTIVE = "✅"
    PAUSED = "⏸️"
    DISABLED = "⚠️"
    BACK = "⬅️"
    FORWARD = "➡️"
    SAVE = "💾"
    CANCEL = "❌"
    SUCCESS = "✅"
    ERROR = "❌"
    WARNING = "⚠️"
    INFO = "ℹ️"
    BUTTON = "🔘"
    LINK = "🔗"
    LIST = "📋"
    HOME = "🏠"
