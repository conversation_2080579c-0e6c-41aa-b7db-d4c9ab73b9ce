"""
Validation utilities for ButtonBot
Handles input validation and data sanitization
"""

import re
import validators
from typing import Op<PERSON>, <PERSON>ple
from urllib.parse import urlparse

def validate_url(url: str) -> Tuple[bool, Optional[str]]:
    """
    Validate URL format and accessibility
    Returns (is_valid, error_message)
    """
    if not url:
        return False, "URL cannot be empty"
    
    # Add protocol if missing
    if not url.startswith(('http://', 'https://', 'tg://')):
        url = 'https://' + url
    
    # Check for Telegram-specific URLs
    telegram_patterns = [
        r'^https?://t\.me/',
        r'^https?://telegram\.me/',
        r'^tg://'
    ]
    
    is_telegram_url = any(re.match(pattern, url) for pattern in telegram_patterns)
    
    if is_telegram_url:
        return True, None
    
    # Validate regular URLs
    if validators.url(url):
        return True, None
    else:
        return False, "Invalid URL format"

def validate_button_text(text: str) -> Tuple[bool, Optional[str]]:
    """
    Validate button text
    Returns (is_valid, error_message)
    """
    if not text:
        return False, "Button text cannot be empty"
    
    if len(text) > 64:
        return False, "Button text must be 64 characters or less"
    
    # Check for invalid characters
    invalid_chars = ['<', '>', '&', '"']
    if any(char in text for char in invalid_chars):
        return False, "Button text contains invalid characters"
    
    return True, None

def validate_channel_username(username: str) -> Tuple[bool, Optional[str]]:
    """
    Validate Telegram channel username
    Returns (is_valid, error_message)
    """
    if not username:
        return False, "Channel username cannot be empty"
    
    # Remove @ if present
    username = username.lstrip('@')
    
    # Check username format
    if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', username):
        return False, "Invalid channel username format"
    
    return True, None

def validate_channel_id(channel_id: str) -> Tuple[bool, Optional[str]]:
    """
    Validate Telegram channel ID
    Returns (is_valid, error_message)
    """
    if not channel_id:
        return False, "Channel ID cannot be empty"
    
    # Channel ID should be negative integer for channels
    try:
        channel_id_int = int(channel_id)
        if channel_id_int >= 0:
            return False, "Channel ID must be negative"
        return True, None
    except ValueError:
        return False, "Channel ID must be a number"

def sanitize_text(text: str) -> str:
    """
    Sanitize text input for safe storage and display
    """
    if not text:
        return ""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Trim whitespace
    text = text.strip()
    
    # Limit length
    if len(text) > 1000:
        text = text[:1000]
    
    return text

def validate_buttons_per_row(count: int) -> Tuple[bool, Optional[str]]:
    """
    Validate buttons per row count
    Returns (is_valid, error_message)
    """
    if count < 1:
        return False, "Must have at least 1 button per row"
    
    if count > 5:
        return False, "Cannot have more than 5 buttons per row"
    
    return True, None

def extract_channel_info(text: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Extract channel username or ID from various input formats
    Returns (channel_id, channel_username)
    """
    text = text.strip()
    
    # Handle t.me links
    if 't.me/' in text:
        match = re.search(r't\.me/([a-zA-Z][a-zA-Z0-9_]{4,31})', text)
        if match:
            return None, match.group(1)
    
    # Handle @username format
    if text.startswith('@'):
        username = text[1:]
        if re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', username):
            return None, username
    
    # Handle channel ID
    if text.startswith('-'):
        try:
            channel_id = int(text)
            return str(channel_id), None
        except ValueError:
            pass
    
    # Handle plain username
    if re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', text):
        return None, text
    
    return None, None
