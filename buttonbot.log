2025-08-08 04:34:14,579 - __main__ - INFO - Configuration validated successfully
2025-08-08 04:34:15,039 - __main__ - INFO - ButtonBot started successfully
2025-08-08 04:34:15,040 - __main__ - INFO - Bot username: @auto_button_robot
2025-08-08 04:34:15,812 - apscheduler.scheduler - INFO - Scheduler started
2025-08-08 04:34:16,452 - database.operations - INFO - User 8153676253 created/updated successfully
2025-08-08 04:34:17,824 - database.operations - INFO - User 8153676253 created/updated successfully
2025-08-08 04:34:23,891 - database.operations - INFO - User 8153676253 created/updated successfully
2025-08-08 04:34:36,601 - database.operations - INFO - User 8153676253 created/updated successfully
2025-08-08 04:37:43,932 - database.operations - INFO - User 8153676253 created/updated successfully
2025-08-08 04:37:46,983 - database.operations - INFO - User 8153676253 created/updated successfully
2025-08-08 04:39:32,086 - database.operations - INFO - User 8153676253 created/updated successfully
2025-08-08 04:57:48,422 - __main__ - INFO - Configuration validated successfully
2025-08-08 04:57:49,076 - __main__ - INFO - ButtonBot started successfully
2025-08-08 04:57:49,076 - __main__ - INFO - Bot username: @auto_button_robot
2025-08-08 04:57:49,935 - apscheduler.scheduler - INFO - Scheduler started
2025-08-08 04:57:50,530 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:57:54,743 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:57:55,951 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:00,653 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:02,350 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:07,804 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:10,257 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:13,842 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:17,428 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:23,043 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:28,702 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:36,879 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:45,014 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:58:57,065 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:59:01,261 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:59:18,912 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:59:23,123 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:59:49,282 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 04:59:53,557 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:00:24,215 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:00:28,414 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:00:59,038 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:01:03,256 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:01:33,908 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:01:38,116 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:02:08,743 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:02:12,946 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:02:43,570 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:02:47,763 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:03:18,385 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:03:22,580 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:03:53,346 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-08-08 05:03:57,542 - __main__ - ERROR - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
