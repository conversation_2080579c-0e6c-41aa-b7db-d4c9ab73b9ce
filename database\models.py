"""
Database models for ButtonBot
Defines data structures for users, channels, and buttons
"""

from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass, field
from enum import Enum

class ChannelStatus(Enum):
    """Channel status enumeration"""
    ACTIVE = "active"
    PAUSED = "paused"
    DISABLED = "disabled"

@dataclass
class Button:
    """Button model for channel buttons"""
    text: str
    url: str
    row: int = 0
    position: int = 0
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for MongoDB storage"""
        return {
            'text': self.text,
            'url': self.url,
            'row': self.row,
            'position': self.position
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Button':
        """Create Button from dictionary"""
        return cls(
            text=data['text'],
            url=data['url'],
            row=data.get('row', 0),
            position=data.get('position', 0)
        )

@dataclass
class Channel:
    """Channel model for user channels"""
    channel_id: str
    channel_name: str
    channel_username: Optional[str]
    user_id: int
    status: ChannelStatus = ChannelStatus.DISABLED
    buttons: List[Button] = field(default_factory=list)
    max_buttons_per_row: int = 3
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for MongoDB storage"""
        return {
            'channel_id': self.channel_id,
            'channel_name': self.channel_name,
            'channel_username': self.channel_username,
            'user_id': self.user_id,
            'status': self.status.value,
            'buttons': [button.to_dict() for button in self.buttons],
            'max_buttons_per_row': self.max_buttons_per_row,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Channel':
        """Create Channel from dictionary"""
        return cls(
            channel_id=data['channel_id'],
            channel_name=data['channel_name'],
            channel_username=data.get('channel_username'),
            user_id=data['user_id'],
            status=ChannelStatus(data.get('status', 'disabled')),
            buttons=[Button.from_dict(btn) for btn in data.get('buttons', [])],
            max_buttons_per_row=data.get('max_buttons_per_row', 3),
            created_at=data.get('created_at', datetime.utcnow()),
            updated_at=data.get('updated_at', datetime.utcnow())
        )

@dataclass
class User:
    """User model for bot users"""
    user_id: int
    username: Optional[str]
    first_name: str
    last_name: Optional[str]
    language_code: Optional[str] = 'en'
    is_premium: bool = False
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_active: datetime = field(default_factory=datetime.utcnow)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for MongoDB storage"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'language_code': self.language_code,
            'is_premium': self.is_premium,
            'created_at': self.created_at,
            'last_active': self.last_active
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'User':
        """Create User from dictionary"""
        return cls(
            user_id=data['user_id'],
            username=data.get('username'),
            first_name=data['first_name'],
            last_name=data.get('last_name'),
            language_code=data.get('language_code', 'en'),
            is_premium=data.get('is_premium', False),
            created_at=data.get('created_at', datetime.utcnow()),
            last_active=data.get('last_active', datetime.utcnow())
        )
