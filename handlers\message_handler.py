"""
Message monitoring handler for ButtonBot
Handles automatic button addition to channel posts
"""

import logging
from telegram import Update, Message, ChatMemberUpdated
from telegram.ext import ContextTypes, MessageHandler, filters, ChatMemberHandler
from telegram.error import TelegramError

from config import Emojis
from database.models import ChannelStatus, Channel
from database.operations import db_manager
from utils.keyboards import generate_post_buttons, main_menu_keyboard
from utils.helpers import safe_send_photo_message, truncate_caption

logger = logging.getLogger(__name__)

async def handle_channel_post(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle new posts in monitored channels"""
    if not update.channel_post:
        return
    
    message = update.channel_post
    channel_id = str(message.chat.id)
    
    try:
        # Get all active channels for this channel ID
        active_channels = await db_manager.get_active_channels()
        target_channel = None
        
        for channel in active_channels:
            if channel.channel_id == channel_id:
                target_channel = channel
                break
        
        if not target_channel:
            # Channel not monitored or not active
            return
        
        # Check if message already has buttons (avoid duplicate processing)
        if message.reply_markup and message.reply_markup.inline_keyboard:
            logger.info(f"Message in {channel_id} already has buttons, skipping")
            return
        
        # Generate buttons for the post
        if not target_channel.buttons:
            logger.info(f"No buttons configured for channel {channel_id}")
            return
        
        button_markup = generate_post_buttons(
            target_channel.buttons, 
            target_channel.max_buttons_per_row
        )
        
        if not button_markup:
            logger.warning(f"Failed to generate button markup for channel {channel_id}")
            return
        
        # Add buttons to the message
        await context.bot.edit_message_reply_markup(
            chat_id=channel_id,
            message_id=message.message_id,
            reply_markup=button_markup
        )
        
        logger.info(f"Successfully added buttons to message {message.message_id} in channel {channel_id}")
        
    except TelegramError as e:
        logger.error(f"Telegram error adding buttons to channel {channel_id}: {e}")
        
        # If bot doesn't have permissions, disable the channel
        if "not enough rights" in str(e).lower() or "forbidden" in str(e).lower():
            if target_channel:
                target_channel.status = ChannelStatus.DISABLED
                await db_manager.update_channel(target_channel)
                logger.warning(f"Disabled channel {channel_id} due to permission error")
        
    except Exception as e:
        logger.error(f"Unexpected error adding buttons to channel {channel_id}: {e}")

async def handle_edited_channel_post(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle edited posts in monitored channels"""
    if not update.edited_channel_post:
        return
    
    message = update.edited_channel_post
    channel_id = str(message.chat.id)
    
    try:
        # Get all active channels for this channel ID
        active_channels = await db_manager.get_active_channels()
        target_channel = None
        
        for channel in active_channels:
            if channel.channel_id == channel_id:
                target_channel = channel
                break
        
        if not target_channel:
            return
        
        # Check if the edited message lost its buttons
        if not message.reply_markup or not message.reply_markup.inline_keyboard:
            # Re-add buttons if they were removed
            if target_channel.buttons:
                button_markup = generate_post_buttons(
                    target_channel.buttons, 
                    target_channel.max_buttons_per_row
                )
                
                if button_markup:
                    await context.bot.edit_message_reply_markup(
                        chat_id=channel_id,
                        message_id=message.message_id,
                        reply_markup=button_markup
                    )
                    
                    logger.info(f"Re-added buttons to edited message {message.message_id} in channel {channel_id}")
        
    except TelegramError as e:
        logger.error(f"Telegram error handling edited post in channel {channel_id}: {e}")
    except Exception as e:
        logger.error(f"Unexpected error handling edited post in channel {channel_id}: {e}")

async def handle_private_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle private messages to the bot"""
    if not update.message or update.message.chat.type != 'private':
        return

    # If user sends a message outside of conversation, show help
    help_text = f"""
{Emojis.INFO} *How to use ButtonBot*

Use these commands to interact with me:

{Emojis.ROBOT} `/start` - Main menu and welcome
{Emojis.CHANNEL} `/mychannels` - Manage your channels
{Emojis.SETTINGS} `/settings` - Quick settings access
{Emojis.HELP} `/help` - Detailed help and documentation

{Emojis.WARNING} *Note:* If you were in the middle of adding a button or channel, please start over using the commands above.

Choose an option:
"""

    # Send with persistent image
    await safe_send_photo_message(
        update, context, help_text,
        reply_markup=main_menu_keyboard()
    )

async def handle_bot_added_to_channel(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle when bot is added as admin to a channel"""
    if not update.my_chat_member:
        return

    chat_member_update = update.my_chat_member
    chat = chat_member_update.chat
    new_member = chat_member_update.new_chat_member
    old_member = chat_member_update.old_chat_member

    # Check if this is a channel and bot was promoted to admin
    if (chat.type not in ['channel', 'supergroup'] or
        new_member.status not in ['administrator', 'creator'] or
        old_member.status == 'administrator'):
        return

    # Get the user who added the bot (from the update)
    user_who_added = chat_member_update.from_user
    if not user_who_added:
        logger.warning(f"Could not determine who added bot to channel {chat.id}")
        return

    try:
        # Check if channel already exists for this user
        existing_channel = await db_manager.get_channel(user_who_added.id, str(chat.id))
        if existing_channel:
            logger.info(f"Channel {chat.id} already exists for user {user_who_added.id}")
            return

        # Check if user has reached channel limit
        user_channels = await db_manager.get_user_channels(user_who_added.id)
        if len(user_channels) >= 10:  # MAX_CHANNELS_PER_USER
            # Send limit reached message
            limit_message = f"{Emojis.ERROR} *Channel Limit Reached*\n\n" \
                          f"You can add up to 10 channels. Please remove some channels before adding new ones.\n\n" \
                          f"Use /mychannels to manage your existing channels."

            truncated_limit_message = truncate_caption(limit_message)

            try:
                with open('start.jpg', 'rb') as photo:
                    await context.bot.send_photo(
                        chat_id=user_who_added.id,
                        photo=photo,
                        caption=truncated_limit_message,
                        reply_markup=main_menu_keyboard(),
                        parse_mode='Markdown'
                    )
            except FileNotFoundError:
                await context.bot.send_message(
                    chat_id=user_who_added.id,
                    text=truncated_limit_message,
                    reply_markup=main_menu_keyboard(),
                    parse_mode='Markdown'
                )
            return

        # Create new channel entry
        new_channel = Channel(
            channel_id=str(chat.id),
            channel_name=chat.title or "Unknown Channel",
            channel_username=chat.username,
            user_id=user_who_added.id,
            status=ChannelStatus.DISABLED  # Start disabled until user configures buttons
        )

        success = await db_manager.create_channel(new_channel)

        if success:
            # Send success notification to the user
            success_message = f"""
{Emojis.SUCCESS} *Channel Added Successfully!*

Your channel '{chat.title}' has been added to ButtonBot!

{Emojis.INFO} *Next Steps:*
1. Configure your custom buttons
2. Set button layout preferences
3. Enable automatic button addition
4. All new posts will include your buttons!

{Emojis.SETTINGS} *Current Status:* {Emojis.DISABLED} Disabled
The channel is currently disabled. Use "View All Channels" to configure and enable it.
"""

            # Truncate message if needed
            truncated_message = truncate_caption(success_message)

            try:
                with open('start.jpg', 'rb') as photo:
                    try:
                        await context.bot.send_photo(
                            chat_id=user_who_added.id,
                            photo=photo,
                            caption=truncated_message,
                            reply_markup=channel_added_keyboard(),
                            parse_mode='Markdown'
                        )
                    except Exception as caption_error:
                        if "Media_caption_too_long" in str(caption_error):
                            # Further truncate if still too long
                            shorter_message = truncate_caption(truncated_message, 800)
                            photo.seek(0)  # Reset file pointer
                            await context.bot.send_photo(
                                chat_id=user_who_added.id,
                                photo=photo,
                                caption=shorter_message,
                                reply_markup=channel_added_keyboard(),
                                parse_mode='Markdown'
                            )
                        else:
                            raise caption_error
            except FileNotFoundError:
                await context.bot.send_message(
                    chat_id=user_who_added.id,
                    text=truncated_message,
                    reply_markup=channel_added_keyboard(),
                    parse_mode='Markdown'
                )

            logger.info(f"Successfully added channel {chat.id} ({chat.title}) for user {user_who_added.id}")
        else:
            logger.error(f"Failed to save channel {chat.id} to database")

    except Exception as e:
        logger.error(f"Error handling bot added to channel {chat.id}: {e}")
        try:
            try:
                with open('start.jpg', 'rb') as photo:
                    await context.bot.send_photo(
                        chat_id=user_who_added.id,
                        photo=photo,
                        caption=f"{Emojis.ERROR} *Error Adding Channel*\n\n"
                               f"Something went wrong while adding your channel. Please try again or contact support.",
                        reply_markup=main_menu_keyboard(),
                        parse_mode='Markdown'
                    )
            except FileNotFoundError:
                await context.bot.send_message(
                    chat_id=user_who_added.id,
                    text=f"{Emojis.ERROR} *Error Adding Channel*\n\n"
                         f"Something went wrong while adding your channel. Please try again or contact support.",
                    reply_markup=main_menu_keyboard(),
                    parse_mode='Markdown'
                )
        except Exception as send_error:
            logger.error(f"Failed to send error message to user {user_who_added.id}: {send_error}")

# Handler registration
def register_message_handlers(application):
    """Register message-related handlers"""
    # Channel post handlers (for automatic button addition)
    application.add_handler(
        MessageHandler(filters.UpdateType.CHANNEL_POST, handle_channel_post),
        group=1
    )
    application.add_handler(
        MessageHandler(filters.UpdateType.EDITED_CHANNEL_POST, handle_edited_channel_post),
        group=1
    )

    # Bot admin status change handler (for auto-detection)
    application.add_handler(
        ChatMemberHandler(handle_bot_added_to_channel, ChatMemberHandler.MY_CHAT_MEMBER),
        group=0
    )

    # Private message handler (fallback for unhandled messages)
    application.add_handler(
        MessageHandler(filters.TEXT & filters.ChatType.PRIVATE, handle_private_message),
        group=2
    )
